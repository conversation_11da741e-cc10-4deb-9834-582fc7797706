/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.service.batch.importack;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.enums.database.FileOptionStatus;
import com.howbuy.tms.common.enums.database.FileTypeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessDtlRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.FundFileProcessRecPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaBusinessBatchFlowPo;
import com.howbuy.tms.high.batch.dao.po.batch.TaskDependDefCfgPo;
import com.howbuy.tms.high.batch.facade.enums.BusinessProcessingStepEnum;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.repository.BusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.FundFileProcessDtlRecRepository;
import com.howbuy.tms.high.batch.service.repository.FundFileProcessRecRepository;
import com.howbuy.tms.high.batch.service.repository.TaBusinessBatchFlowRepository;
import com.howbuy.tms.high.batch.service.repository.TaskDependDefCfgRepository;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @description:导入确认服务类
 * <AUTHOR>
 * @date 2017年7月12日 上午9:32:47
 * @since JDK 1.6
 */
@Service("importAckService")
public class ImportAckService {

    private static Logger logger = LogManager.getLogger(ImportAckService.class);

    @Autowired
    private AckFileRecService ackFileRecService;

    @Autowired
    private BusinessBatchFlowRepository businessBatchFlowRepository;

    @Autowired
    private TaskDependDefCfgRepository taskDependDefCfgRepository;

    @Autowired
    private FundFileProcessRecRepository fundFileProcessRecRepository;
    
    @Autowired
    private FundFileProcessDtlRecRepository fundFileProcessDtlRecRepository;
    
    @Autowired
    private TaBusinessBatchFlowRepository taBusinessBatchFlowRepository;
    
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;

    /**
     * flowCheck:流程检查
     * 
     * @param tradeDt
     * @param taCode
     * <AUTHOR>
     * @date 2017年7月12日 上午10:28:14
     */
    public void flowCheck(String tradeDt, String taCode, String sysCode) {
        String taskId = BusinessProcessingStepEnum.BPS_IMPORT_ACK_FILE.getCode();

        // 日初始化已完成
        // 流程任务项检查
        List<TaskDependDefCfgPo> taskPreDependList = taskDependDefCfgRepository.selectPreUnCompletedTask(taskId, tradeDt, sysCode);
        if (CollectionUtils.isNotEmpty(taskPreDependList)) {
            // 前置节点状态未完成，不能执行当前操作
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_PRE_UNCOMPLETED, "处理执行失败！日初始化未完成", false);
        }

        // 确认日终处理未完成
        List<TaskDependDefCfgPo> taskPostDependList = taskDependDefCfgRepository.selectPostAlreadyExecTATask(taskId, tradeDt, sysCode, taCode);
        if (CollectionUtils.isNotEmpty(taskPostDependList)) {
            // 后置节点已经开始处理，不能执行当前操作
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_POST_ALREADY_START, "批处理执行失败！确认日终处理已开始执行或处理完成", false);
        }

        TaBusinessBatchFlowPo taBusinessBatchFlowPo = taBusinessBatchFlowRepository.selectByTaCodeAndSysCodeAndTradeDtAndTaskId(taCode, sysCode, tradeDt, taskId);

        if (taBusinessBatchFlowPo == null) {
            // 当前任务不存在
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_BATCH_FLOW_ERROR, "当前TA任务不存在");
        }

        if (BatchStatEnum.PROCESSING.getKey().equals(taBusinessBatchFlowPo.getFlowStat())) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_PROCESSING, "当前TA任务正在执行中", false);
        }
        
        if (BatchStatEnum.PROCESS_SUCCESS.getKey().equals(taBusinessBatchFlowPo.getFlowStat())) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FLOW_PROCESSING, "当前TA任务已处理完成", false);
        }
        FundFileProcessRecPo rec = fundFileProcessRecRepository.selectFundFileProcessRec(tradeDt, FileTypeEnum.H_TRADE_ACK.getCode(), taCode);
        if (rec == null) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FILE_PROCESS_ERROR, "当前TA确认文件的ok文件未到不允许执行", false);
        }
        
        if (FileOptionStatus.PROCESSING.getCode().equals(rec.getFileOpStatus())) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FILE_PROCESS_ERROR, "当前TA文件正在执行中", false);
        }
        
        // 校验文件表总数和该TA下的产品文件总数是否一致
        int count = fundFileProcessDtlRecRepository.countFundFileDtlProcessRec(tradeDt, FileTypeEnum.H_TRADE_ACK.getCode(), taCode);
        if (rec.getFundFileCount().compareTo(new BigDecimal(count)) != 0) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FILE_PROCESS_ERROR, "确认文件总数与实际文件个数不一致", false);
        }

        count = fundFileProcessDtlRecRepository.countGenFailRec(tradeDt, FileTypeEnum.H_TRADE_ACK.getCode(), taCode);
        if (count > 0) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_FILE_PROCESS_ERROR, "当前TA存在未生成成功的确认文件");
        }
        
        // 当前任务如果未开始执行，更新为执行中
        String startTm = String.format("%1$tH%1$tM%1$tS", new Date());
        taBusinessBatchFlowRepository.updateBatchStatAndRelationId(taCode, taskId, tradeDt, BatchStatEnum.PROCESSING.getKey(), taBusinessBatchFlowPo.getFlowStat(), sysCode, startTm, null, rec.getRecordNo());
    }

    /**
     * execImportAck:执行导入确认
     * 
     * @param tradeDt
     * @param taCode
     * <AUTHOR>
     * @date 2017年7月12日 上午10:03:21
     */
    public List<String> execImportAck(String tradeDt, String taCode, String sysCode) {
        String fileType = FileTypeEnum.H_TRADE_ACK.getCode();
        List<FundFileProcessDtlRecPo> fileList = fundFileProcessDtlRecRepository.selectFundFileProcessDtlRecList(tradeDt, fileType, taCode);
        if (CollectionUtils.isEmpty(fileList)) {
            logger.info("ImportAckService|execImportAck|ackFileList is empty,taCode:{}", taCode);
            return null;
        }
        
        // 先删除
        ackFileRecService.deleteByTaCode(taCode);
        // 查询该TA下所有高端产品
        List<String> productList = new ArrayList<>();
        List<HighProductBaseInfoModel> beanList = queryHighProductOuterService.getHighProductBaseInfoByTaCode(taCode);
        if (CollectionUtils.isNotEmpty(beanList)) {
            for (HighProductBaseInfoModel model : beanList) {
                productList.add(model.getFundCode());
            }
        }
        logger.info("importAckFileProcessor.taCode:{},productList:{}", taCode, JSON.toJSONString(productList));
        
        List<String> failList = new ArrayList<>();
        FundFileProcessDtlRecPo po = new FundFileProcessDtlRecPo();
        po.setFileType(FileTypeEnum.H_TRADE_ACK.getCode());
        po.setTaCode(taCode);
        po.setTaTradeDt(tradeDt);
        try {
            // 更文件处理状态
            po.setFileOpStatus(FileOptionStatus.PROCESSING.getCode());
            updateFundFileProcessStatus(po);
            int count = ackFileRecService.addList(taCode);
            logger.info("ackFileRecService.addList({}),count:{}", taCode, count);

            po.setFileOpStatus(FileOptionStatus.IMPORT_SUCCESS.getCode());
            updateFundFileProcessStatus(po);
            logger.info("fundFileProcessRecService.update,response:{}", JSON.toJSONString(po));
        } catch (Exception e) {
            po.setFileOpStatus(FileOptionStatus.IMPORT_FAIL.getCode());
            if (e instanceof BusinessException) {
                po.setMemo(MessageSource.getMessageByCode(((BusinessException) e).getErrorCode()));
            } else {
                po.setMemo("文件处理发生异常");
            }
            updateFundFileProcessStatus(po);
            failList.add(po.getFundCode());
            logger.error(po.getMemo(), e);
        }
        return failList;
    }

    /**
     * updateFundFileProcessStatus:更新状态
     * 
     * @param po
     * <AUTHOR>
     * @date 2017年7月12日 下午1:33:33
     */
    private void updateFundFileProcessStatus(FundFileProcessDtlRecPo po) {
        fundFileProcessDtlRecRepository.updateProcessStatus(po.getTaTradeDt(), po.getTaCode(), po.getFileType(), po.getFileOpStatus(), new Date());
    }

    /**
     * endoff:endoff
     * 
     * @param tradeDt
     * <AUTHOR>
     * @date 2017年7月21日 下午5:22:13
     */
    public void endTaoff(String tradeDt, String taCode, String sysCode, String flowStat) {
        String taskId = BusinessProcessingStepEnum.BPS_IMPORT_ACK_FILE.getCode();
        String endTm = String.format("%1$tH%1$tM%1$tS", new Date());
        String fileOpStatus = FileOptionStatus.IMPORT_SUCCESS.getCode();
        if (!BatchStatEnum.PROCESS_SUCCESS.getKey().equals(flowStat)) {
            fileOpStatus = FileOptionStatus.IMPORT_FAIL.getCode();
        }
        // 更新文件表
        fundFileProcessRecRepository.updateFileOpStatus(tradeDt, taCode, FileTypeEnum.H_TRADE_ACK.getCode(), fileOpStatus, null);
        // 处理中更新成处理失败/成功
        taBusinessBatchFlowRepository.updateBatchStat(taCode, taskId, tradeDt, flowStat, BatchStatEnum.PROCESSING.getKey(), sysCode, null, endTm);
    }
    /**
     * 
     * endAllOff:TA业务处理完更新主表
     * @param tradeDt
     * @param sysCode
     * <AUTHOR>
     * @date 2018年12月5日 下午4:14:46
     */
    public void endAllOff(String tradeDt, String sysCode) {
        String taskId = BusinessProcessingStepEnum.BPS_IMPORT_ACK_FILE.getCode();
        String endTm = String.format("%1$tH%1$tM%1$tS", new Date());
        
        Integer count = taBusinessBatchFlowRepository.countTaBusinessBatchFlowNoProcessByTaskId(tradeDt, taskId, sysCode);
        if (count == 0) {
            businessBatchFlowRepository.updateBatchStatus(tradeDt, taskId, BatchStatEnum.PROCESS_SUCCESS.getKey(), endTm, endTm, sysCode);
        }
    }

}
