<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.batch.dao.mapper.customize.batch.CxgPaymentCheckOrderPoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.batch.dao.po.batch.CxgPaymentCheckOrderPo"
               extends="com.howbuy.tms.high.batch.dao.mapper.batch.CxgPaymentCheckOrderPoAutoMapper.BaseResultMap">
    </resultMap>

	<update id="deleteCXGPaymentCheckOrder">
		delete a from CXG_PAYMENT_CHECK_ORDER a where a.SYS_CODE = #{sysCode,jdbcType=VARCHAR}
	</update>

	<!-- 从临时表插入储蓄罐支付对账订单 -->
	<insert id="insertCXGPaymentCheckOrderFromTempTable">
		INSERT INTO CXG_PAYMENT_CHECK_ORDER
		(CXG_ORDER_NO,
		TX_ACCT_NO,
		DIS_CODE,
		CP_ACCT_NO,
		OUT_ORDER_NO,
		APP_VOL,
		APP_DT,
		APP_TM,
		ORDER_STATUS,
		PAYMENT_STATUS,
		BANK_ACCT,
		BANK_CODE,
		BANK_NAME,
		SYS_CODE)
		SELECT CXG_ORDER_NO,
		TX_ACCT_NO,
		DIS_CODE,
		CP_ACCT_NO,
		OUT_ORDER_NO,
		APP_VOL,
		APP_DT,
		APP_TM,
		ORDER_STATUS,
		PAYMENT_STATUS,
		BANK_ACCT,
		BANK_CODE,
		BANK_NAME,
		#{sysCode,jdbcType=VARCHAR}
		FROM ${tableName}

	</insert>

	<!-- 根据支付方式统计-支付订单统计总笔数/总金额 -->
	<resultMap id="TotalOrderNumAndTotalAmtMap"
		type="com.howbuy.tms.high.batch.dao.vo.PaymentOrderCountVo">
		<result column="payType" jdbcType="VARCHAR" property="payType" />
		<result column="totalOrderNum" jdbcType="DECIMAL" property="totalOrderNum" />
		<result column="totalAmt" jdbcType="DECIMAL" property="totalAmt" />
	</resultMap>
	<select id="selectTotalOrderNumAndTotalAmt" resultMap="TotalOrderNumAndTotalAmtMap">
		select
		ifnull(count(1), 0) as totalOrderNum, ifnull(sum(t1.app_vol), 0) as totalAmt
		from cxg_payment_check_order t1
		where t1.payment_status = '2' 
		and t1.sys_code = #{sysCode,jdbcType=VARCHAR}
	</select>
	
	<update id="updateSysCode" parameterType="map">
	    update CXG_PAYMENT_CHECK_ORDER
	    set SYS_CODE = #{sysCode,jdbcType=VARCHAR}
	    where SYS_CODE IN ('93','94')
	</update>
  
    <!-- 支付对账订单结果Map -->
	<resultMap id="PmtCheckAckResult" type="com.howbuy.tms.high.batch.dao.vo.PmtCheckAckVo">
	    <result column="PMT_DEAL_NO" jdbcType="VARCHAR" property="pmtDealNo" />
	    <result column="PMT_AMT" jdbcType="DECIMAL" property="pmtAmt" />
	    <result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType" />
 	</resultMap>
	
	<select id="selectPaymentOrderForPayChkAck" parameterType="map" resultMap="PmtCheckAckResult">
		 select t.out_order_no as PMT_DEAL_NO, t.app_vol as PMT_AMT
		  from cxg_payment_check_order t
		 where t.payment_status = '2'
		   and t.sys_code = #{sysCode,jdbcType=VARCHAR}
	</select>

  <delete id="deleteBySysCode">
    delete from CXG_PAYMENT_CHECK_ORDER where sys_code = #{sysCode,jdbcType=VARCHAR}
    </delete>

	<insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
		<!--@mbg.generated-->
		insert into cxg_payment_check_order
		(cxg_order_no, tx_acct_no, dis_code, cp_acct_no, out_order_no, app_vol, app_dt, app_tm,
		order_status, payment_status, bank_acct, bank_code, bank_name, sys_code)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.cxgOrderNo,jdbcType=VARCHAR}, #{item.txAcctNo,jdbcType=VARCHAR}, #{item.disCode,jdbcType=VARCHAR},
			#{item.cpAcctNo,jdbcType=VARCHAR}, #{item.outOrderNo,jdbcType=VARCHAR}, #{item.appVol,jdbcType=DECIMAL},
			#{item.appDt,jdbcType=VARCHAR}, #{item.appTm,jdbcType=VARCHAR}, #{item.orderStatus,jdbcType=VARCHAR},
			#{item.paymentStatus,jdbcType=VARCHAR}, #{item.bankAcct,jdbcType=VARCHAR}, #{item.bankCode,jdbcType=VARCHAR},
			#{item.bankName,jdbcType=VARCHAR}, #{item.sysCode,jdbcType=VARCHAR})
		</foreach>
	</insert>

</mapper>