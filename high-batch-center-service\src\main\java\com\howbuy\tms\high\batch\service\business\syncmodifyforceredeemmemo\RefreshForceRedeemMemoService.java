/**
 * Copyright (c) 2017, ShangH<PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */
package com.howbuy.tms.high.batch.service.business.syncmodifyforceredeemmemo;

import com.howbuy.interlayer.product.service.TradeDayService;
import com.howbuy.tms.common.datasource.DynamicDataSourceHelper;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.queryforceredeemmemo.ForceRedeemMemoBean;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.queryforceredeemmemo.QueryForceRedeemMemoOutContext;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.queryforceredeemmemo.QueryForceRedeemMemoOutRst;
import com.howbuy.tms.common.outerservice.fbsonlinesearch.queryforceredeemmemo.QueryForceRedeemMemoOutService;
import com.howbuy.tms.high.batch.dao.po.order.SimuFundCheckOrderPo;
import com.howbuy.tms.high.batch.service.repository.SimuFundCheckOrderRepository;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @className RefreshForceRedeemMemo
 * @description
 * @date 2020/4/7 10:47
 */
@Service("refreshForceRedeemMemoService")
public class RefreshForceRedeemMemoService {
    private static final Logger logger = LoggerFactory.getLogger(RefreshForceRedeemMemoService.class);

    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;

    @Autowired
    private WorkdayService workdayService;

    @Autowired
    private TradeDayService tradeDayService;

    @Autowired
    private QueryForceRedeemMemoOutService queryForceRedeemMemoOutService;

    public void process() {
        String workDay = workdayService.getSaleSysCurrWorkay();

        List<String> ackDtList = new ArrayList<>();
        ackDtList.add(workDay);
        for (int i = 1; i < 5; i++) {
            String startTradeDt = tradeDayService.addTradeDays(workDay, i * -1);
            ackDtList.add(startTradeDt);
        }

        for (String actDt : ackDtList) {
            refreshForceMemo(actDt, null);
        }
    }

    /**
     * @param actDt
     * @return
     * @Description 刷新强减原因
     * <AUTHOR>
     * @Date 2020/4/7 11:18
     **/
    public void refreshForceMemo(String actDt, List<String> disTransSeqs) {
        int pageSize = 500;
        int pageNo = 1;
        long totalSize = 0;
        long alreadyCheckSize = -1;
        // 第一次查询的时候需要查询总数，其他查询不需要
        boolean isCount = true;
        while (alreadyCheckSize < totalSize) {
            // 查询所有数据源分页数据
            QueryForceRedeemMemoOutContext context = new QueryForceRedeemMemoOutContext();
            context.setPageNum(pageNo);
            context.setPageSize(pageSize);
            context.setTradeDt(actDt);
            context.setDisTransSeqs(disTransSeqs);
            QueryForceRedeemMemoOutRst queryForceRedeemMemoOutRst = queryForceRedeemMemoOutService.queryForceRedeemMemo(context);
            if (queryForceRedeemMemoOutRst == null || CollectionUtils.isEmpty(queryForceRedeemMemoOutRst.getList())) {
                logger.warn("RefreshForceRedeemMemoService|pageResult is null, taTradeDt:{}, pageNo:{}", new Object[]{actDt, pageNo});
                return;
            }

            totalSize = (totalSize == 0) ? queryForceRedeemMemoOutRst.getTotal() : totalSize;
            pageNo++;

            List<ForceRedeemMemoBean> forceRedeemMemoList = queryForceRedeemMemoOutRst.getList();
            int singleSize = forceRedeemMemoList.size();
            alreadyCheckSize = (alreadyCheckSize < 0) ? singleSize : (alreadyCheckSize + singleSize);

            Map<String, List<SimuFundCheckOrderPo>> simuFundCheckOrderPoMap = buildUpdateSimuCheck(forceRedeemMemoList);

            // 批量更新强减原因
            int updateCount = batchUpdate(simuFundCheckOrderPoMap);

            // 属性强减原因
            logger.info("RefreshForceRedeemMemoService|list size:{}, updateCount", singleSize, updateCount);
        }
        logger.info("RefreshForceRedeemMemoService|refreshForceMemo|total list size:{}", totalSize);
    }

    private Map<String, List<SimuFundCheckOrderPo>> buildUpdateSimuCheck(List<ForceRedeemMemoBean> list) {
        SimuFundCheckOrderPo simuFundCheckOrderPo = null;
        Map<String, List<SimuFundCheckOrderPo>> simuFundCheckOrderPoMap = new HashMap<>();
        for (ForceRedeemMemoBean forceRedeemMemoBean : list) {
            String ds = DynamicDataSourceHelper.getTmsOrdersDSByTxAcctNo(forceRedeemMemoBean.getCustNo());
            simuFundCheckOrderPo = new SimuFundCheckOrderPo();
            simuFundCheckOrderPo.setDisDealAckNo(forceRedeemMemoBean.getDisTransSeq());
            simuFundCheckOrderPo.setForceRedeemMemo(forceRedeemMemoBean.getReason());
            simuFundCheckOrderPo.setTxAcctNo(forceRedeemMemoBean.getCustNo());
            List<SimuFundCheckOrderPo> simuFundCheckOrderPoList = simuFundCheckOrderPoMap.get(ds);
            if (simuFundCheckOrderPoList == null) {
                simuFundCheckOrderPoList = new ArrayList<>();
            }
            simuFundCheckOrderPoList.add(simuFundCheckOrderPo);
            simuFundCheckOrderPoMap.put(ds, simuFundCheckOrderPoList);
        }

        return simuFundCheckOrderPoMap;
    }

    /**
     * @param simuFundCheckMap
     * @return int
     * @Description 批量更新强减原因
     * <AUTHOR>
     * @Date 2020/4/8 18:04
     **/
    private int batchUpdate(Map<String, List<SimuFundCheckOrderPo>> simuFundCheckMap) {
        int updateCount = 0;
        for (Map.Entry<String, List<SimuFundCheckOrderPo>> entry : simuFundCheckMap.entrySet()) {
            int count = simuFundCheckOrderRepository.batchUpdateForceRedeemMemo(entry.getValue());
            updateCount += count;
        }
        return updateCount;
    }

}
