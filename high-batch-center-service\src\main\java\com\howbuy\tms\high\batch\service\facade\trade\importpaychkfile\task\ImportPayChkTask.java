/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.trade.importpaychkfile.task;

import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.service.batch.importpaychk.ImportPayChkService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.CountDownLatch;

/**
 * 
 * @description:导入支付对账
 * <AUTHOR>
 * @date 2018年12月4日 下午4:44:47
 * @since JDK 1.6
 */
public class ImportPayChkTask implements Runnable {
	private static Logger logger = LogManager.getLogger(ImportPayChkTask.class);
	
	private CountDownLatch latch;
	private ThreadExceptionStatus exStatus;
	private String tradeDt;
	private String sysCode;
	private ImportPayChkService importPayChkService;
	public ImportPayChkTask(CountDownLatch latch, ThreadExceptionStatus exStatus, String tradeDt, ImportPayChkService importPayChkService,
	       String sysCode) {
		this.latch = latch;
		this.exStatus = exStatus;
		this.tradeDt = tradeDt;
		this.importPayChkService = importPayChkService;
		this.sysCode = sysCode;
	}

	@Override
	public void run() {
		try {
		    importPayChkService.importPayChkFile(tradeDt, sysCode);
            importPayChkService.importPiggyPayChkFile(tradeDt, sysCode);
		} catch (BusinessException e) {
			logger.error("BusinessExceptionError ", e);
			exStatus.setExsitException(true);
			exStatus.setException(e);
            exStatus.setSysCode(sysCode);
		} catch (Throwable ex) {
			logger.error("ThrowableError ", ex);
			exStatus.setExsitException(true);
			exStatus.setException(ex);
			exStatus.setSysCode(sysCode);
		} finally {
			latch.countDown();
		}

	}
}
