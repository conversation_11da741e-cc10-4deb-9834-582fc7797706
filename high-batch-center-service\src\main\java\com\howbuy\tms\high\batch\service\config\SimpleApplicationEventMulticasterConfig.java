package com.howbuy.tms.high.batch.service.config;

import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.context.event.SimpleApplicationEventMulticaster;
import org.springframework.context.support.AbstractApplicationContext;
import org.springframework.core.task.SimpleAsyncTaskExecutor;

/**
 * 异步 event multicaster
 *
 * <AUTHOR>
 * @description: (请在此添加描述)
 * @date 2024/8/23 11:25
 */
@Configuration
public class SimpleApplicationEventMulticasterConfig {

//    @Bean(name = AbstractApplicationContext.APPLICATION_EVENT_MULTICASTER_BEAN_NAME)
    public ApplicationEventMulticaster applicationEventMulticaster() {
        SimpleApplicationEventMulticaster simpleApplicationEventMulticaster = new SimpleApplicationEventMulticaster();
        SimpleAsyncTaskExecutor executor = new SimpleAsyncTaskExecutor();
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.setThreadNamePrefix(Constant.HIGH_BATCH_DEFAULT_EVENT_POOL);
        simpleApplicationEventMulticaster.setTaskExecutor(executor);
        return simpleApplicationEventMulticaster;
    }

}
