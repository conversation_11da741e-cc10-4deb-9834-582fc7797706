/**
 * Copyright (c) 2017, ShangHai HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.service.batch.importpaychk;

import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.busi.PiggyProductTypeEnum;
import com.howbuy.tms.common.outerservice.ftxonlinesearch.piggypaymentcheck.PiggyPaymentCheckOuterService;
import com.howbuy.tms.common.outerservice.payonline.bankacctpaymentcheck.BankAcctPaymentCheckFileResult;
import com.howbuy.tms.common.outerservice.payonline.bankacctpaymentcheck.BankAcctPaymentCheckOuterService;
import com.howbuy.tms.common.utils.StringUtils;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkPathInfo;
import com.howbuy.tms.high.batch.service.common.utils.FileSdkUtil;
import com.howbuy.tms.high.batch.service.config.FilePathStoreBusinessCodeConfig;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.service.file.fileimport.bean.FileImportContext;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.BankAcctPayChkFileImportService;
import com.howbuy.tms.high.batch.service.service.file.fileimport.impl.PiggyPayChkFileImportService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * <AUTHOR>
 * @description:导入支付对账服务类
 * @date 2017年7月6日 下午5:08:35
 * @since JDK 1.6
 */
@Service("importPayChkService")
public class ImportPayChkService {
    private static Logger logger = LogManager.getLogger(ImportPayChkService.class);

    /**
     *
     */
    @Value("${var.getCxgPaymentCheckFileWaitTime}")
    private int waitTime;


    /**
     * 请求生成私募银行账号支付对账文件URL
     */
    @Value("${url.requestGenBankAcctPaymentCheckFile}")
    private String requestGenBankAcctPaymentCheckFileUrl;

    @Autowired
    private BankAcctPaymentCheckOuterService bankAcctPaymentCheckOuterService;

    @Autowired
    private PiggyPaymentCheckOuterService piggyPaymentCheckOuterService;

    @Autowired
    private BankAcctPayChkFileImportService bankAcctPayChkFileImportService;

    @Autowired
    private PiggyPayChkFileImportService piggyPayChkFileImportService;

    /**
     * importPayChkFile:导入支付系统私募对账文件
     *
     * @param tradeDt
     * @throws Exception
     * <AUTHOR>
     * @date 2017年7月6日 下午5:10:38
     */
    public void importPayChkFile(String tradeDt, String sysCode) throws Exception {
        // 银行账号支付对账文件生成申请
        processBankAcctPaymentCheckFile(tradeDt, sysCode);
    }

    ;

    /**
     * importPiggyPayChkFile:导入储蓄罐支付私募对账文件
     *
     * @param tradeDt
     * @throws Exception
     * @throws InterruptedException
     * <AUTHOR>
     * @date 2017年7月6日 下午5:10:56
     */
    public void importPiggyPayChkFile(String tradeDt, String sysCode) throws Exception {
        // 储蓄罐支付对账文件生成申请
        processCxgPaymentCheckFile(tradeDt, sysCode);
    }

    /**
     * processBankAcctPaymentCheckFile:获取支付对账文件，并放到sqlload目录
     *
     * @param tradeDt
     * @return
     * @throws Exception
     * @throws InterruptedException
     * <AUTHOR>
     * @date 2017年7月21日 下午4:25:28
     */
    private void processBankAcctPaymentCheckFile(String tradeDt, String sysCode) throws Exception {
        String url = requestGenBankAcctPaymentCheckFileUrl;
        String merchantCode = com.howbuy.paycommon.model.enums.SysCodeEnum.HOWBUY_MIDDLE_GROUND_PRIVATE.toString();
        // 高端公募
        if ("93".equals(sysCode)) {
            merchantCode = com.howbuy.paycommon.model.enums.SysCodeEnum.HOWBUY_HIGH_END_PRIVATE.toString();
            // TP私募
        } else if ("94".equals(sysCode)) {
            merchantCode = com.howbuy.paycommon.model.enums.SysCodeEnum.HOWBUY_TP_PRIVATE.toString();
        }
        url = url.replaceAll("\\{merchantCode\\}", merchantCode);
        BankAcctPaymentCheckFileResult result = bankAcctPaymentCheckOuterService.genBankAcctPaymentCheckFile(tradeDt, url);
        String fileName = result.getFileName();
        String caseStr = result.getCaseStr();
        if (StringUtils.isEmpty(fileName)) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_BANK_PMT_CHECK_ERROR, caseStr);
        }
        // 文件名称为0时，表示对账数据size为0无需处理文件
        if ("0".equals(fileName)) {
            return;
        }
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.FPS_PAYMENT);
        fileSdkPathInfo.setMiddlePath(merchantCode + File.separator + tradeDt + File.separator + "check");
        fileSdkPathInfo.setFileName(fileName);
        String srcFullPathFileName = FileSdkUtil.getAbsolutePath(fileSdkPathInfo);
        logger.info("processBankAcctPaymentCheckFile-tradeDt:{},fileName:{},srcFullPathFileName:{}", tradeDt, fileName, srcFullPathFileName);
        FileLoadCount fileLoadCount = new FileLoadCount();
        if (isExistsPaymentFileV2(fileLoadCount, fileSdkPathInfo)) {
            // 文件导入
            FileImportContext context = new FileImportContext();
            context.setFileName(fileName);
            context.setRelationPath(fileSdkPathInfo.getMiddlePath());
            context.setTaTradeDt(tradeDt);
            context.getParams().put("sysCode", sysCode);
            bankAcctPayChkFileImportService.process(context);
        }
    }


    /**
     * processCxgPaymentCheckFile:处理储蓄罐支付对账文件
     *
     * @param tradeDt
     * @return
     * @throws Exception
     * <AUTHOR>
     * @date 2016年10月12日 下午10:24:14
     */
    private void processCxgPaymentCheckFile(String tradeDt, String sysCode) throws Exception {
        String productType = PiggyProductTypeEnum.MIDDLE_SM.getCode();
        if ("93".equals(sysCode)) {
            productType = PiggyProductTypeEnum.MIDDLE_HF.getCode();
        } else if ("94".equals(sysCode)) {
            productType = PiggyProductTypeEnum.MIDDLE_TP_SM.getCode();
        }
        String srcFullPathFileName = piggyPaymentCheckOuterService.genPiggyPaymentCheckFile(tradeDt, productType);
        if (StringUtils.isEmpty(srcFullPathFileName)) {
            throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_CXG_PMT_CHECK_ERROR, "请求生成储蓄罐支付对账文件返回异常");
        }
        String fileName = tradeDt + ".txt";
        FileLoadCount fileLoadCount = new FileLoadCount();
        FileSdkPathInfo fileSdkPathInfo = new FileSdkPathInfo();
        fileSdkPathInfo.setFileName(fileName);
        fileSdkPathInfo.setMiddlePath(File.separator + tradeDt + File.separator + productType);
        fileSdkPathInfo.setBusinessCode(FilePathStoreBusinessCodeConfig.CXG_PAYMENT);
        if (isExistsPaymentFileV2(fileLoadCount, fileSdkPathInfo)) {
            // 文件导入
            FileImportContext context = new FileImportContext();
            context.setFileName(fileName);
            context.setRelationPath(fileSdkPathInfo.getMiddlePath());
            context.setTaTradeDt(tradeDt);
            context.getParams().put("sysCode", sysCode);
            piggyPayChkFileImportService.process(context);
        }
    }


    /**
     * isExistsPaymentFile:判断支付对账文件是否存在
     */
    private boolean isExistsPaymentFileV2(FileLoadCount fileLoadCount,  FileSdkPathInfo fileSdkPathInfo) throws Exception {
        fileLoadCount.setCount(fileLoadCount.getCount() + 1);
        if (!FileSdkUtil.exists(fileSdkPathInfo)) {
            if (fileLoadCount.getCount() > 10) {
                throw new BatchException(ExceptionCodes.HIGH_BATCH_CENTER_CXG_PMT_CHECK_ERROR, "请求支付对账确认文件失败,文件不存在");
            }
            logger.info("支付对账文件,一直没有找到,对账文件地址:{},等待时间:{}", FileSdkUtil.getAbsolutePath(fileSdkPathInfo), waitTime);
            Thread.sleep(waitTime);
            return isExistsPaymentFileV2(fileLoadCount, fileSdkPathInfo);
        }
        return true;
    }
}
