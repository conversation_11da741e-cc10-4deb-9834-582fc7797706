/**
 *Copyright (c) 2016, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.trade.task;

import com.alibaba.fastjson.JSON;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.CacheKeyPrefix;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.BatchStatEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.service.batch.importack.ImportAckService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 
 * @description:确认导入
 * <AUTHOR>
 * @date 2018年12月11日 下午5:11:03
 * @since JDK 1.6
 */
public class ImportAckFileTask implements Runnable {

	private static Logger logger = LogManager.getLogger(ImportAckFileTask.class);
	private static final String LOCK_PREFIX = CacheKeyPrefix.HIGH_LOCK_CONCURRENT_PREFIX+"IMPORT_CONFIRM_";
	
	private String taCode;
	private String tradeDt;
	private String sysCode;
	private ImportAckService importAckService;
	private CountDownLatch latch;
	private ThreadExceptionStatus exStatus;
	private LockService lockService;

	public ImportAckFileTask(String taCode, String tradeDt, String sysCode, ImportAckService importAckService,
							 ThreadExceptionStatus exStatus, CountDownLatch latch, LockService lockService) {
		this.taCode = taCode;
		this.tradeDt = tradeDt;
		this.sysCode = sysCode;
		this.importAckService = importAckService;
	    this.latch = latch;
		this.exStatus = exStatus;
		this.lockService = lockService;
	}

	@Override
	public void run() {
		String lockKey = LOCK_PREFIX + taCode + tradeDt + sysCode;
		try {
			if(!lockService.getLock(lockKey)){
				logger.info("ImportAckFileTask|run|getLock false lockKey:{}", lockKey);
				exStatus.setExsitException(true);
				exStatus.setException(new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_IMPORT_ACK_FILE_PROCESS_FAIL, "获取锁失败"));
			} else {
				process();
			}
		} finally {
			latch.countDown();
			lockService.releaseLock(lockKey);
		}
	}

	private void process() {
		try {
			importAckService.flowCheck(tradeDt, taCode, sysCode);
		} catch (BusinessException bb) {
			logger.error("Error ", bb);
			exStatus.setExsitException(true);
			exStatus.setException(bb);
		} catch (Throwable tt) {
			logger.error("Error ", tt);
			exStatus.setExsitException(true);
			exStatus.setException(tt);
		}
		// 校验不通过不往下执行
		if (exStatus.isExsitException()) {
			logger.error("ImportAckFileTask|importAckService.flowCheck throw exception");
			return;
		}

		String flowStat = BatchStatEnum.PROCESS_SUCCESS.getKey();
		try {
			List<String> failTaList = importAckService.execImportAck(tradeDt, taCode, sysCode);
			if (CollectionUtils.isNotEmpty(failTaList)) {
				flowStat = BatchStatEnum.PROCESS_FAIL.getKey();
				exStatus.setExsitException(true);
				exStatus.setException(new BusinessException(ExceptionCodes.HIGH_BATCH_CENTER_IMPORT_ACK_FILE_PROCESS_FAIL, "存在处理失败的文件:" + JSON.toJSONString(failTaList)));
			}
		} catch (BusinessException ab) {
			flowStat = BatchStatEnum.PROCESS_FAIL.getKey();
			logger.error("Error ", ab);
			exStatus.setExsitException(true);
			exStatus.setException(ab);
		} catch (Throwable ac) {
			flowStat = BatchStatEnum.PROCESS_FAIL.getKey();
			logger.error("Error ", ac);
			exStatus.setExsitException(true);
			exStatus.setException(ac);
		} finally {
			try {
				importAckService.endTaoff(tradeDt, taCode, sysCode, flowStat);
			} catch (Exception e) {
				logger.error("ImportAckFileTask|importAckService.endTaoff throw exception,errMsg:{}" , e.getMessage(), e);
			}
		}
	}
}
