package com.howbuy.tms.high.batch.dao.mapper.customize.batch;

import com.howbuy.tms.high.batch.dao.mapper.batch.BankAcctPaymentCheckOrderPoAutoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.BankAcctPaymentCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.PaymentOrderCountVo;
import com.howbuy.tms.high.batch.dao.vo.PmtCheckAckVo;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

/**
 * 去O
 */
public interface BankAcctPaymentCheckOrderPoMapper extends BankAcctPaymentCheckOrderPoAutoMapper {
    /**
     * 
     * deleteBankAcctPaymentCheckOrder:清理银行账户支付对账数据
     * 
     * @param sysCode
     * @return
     * <AUTHOR>
     * @date 2016年10月12日 下午8:18:15
     */
    int deleteBankAcctPaymentCheckOrder(@Param("sysCode") String sysCode);

    /**
     * selectTotalOrderNumAndTotalAmt:(银行卡支付订单统计)
     * 
     * @param sysCode
     * @param busiCode
     * @return
     * <AUTHOR>
     * @date 2016年10月13日 下午9:51:19
     */
    PaymentOrderCountVo selectTotalOrderNumAndTotalAmt(@Param("sysCode") String sysCode, @Param("busiCode") String busiCode);
    /**
     * 
     * selectPaymentOrderForPayChkAck:查询支付对账确认数据
     * 
     * @param sysCode
     * @param busiCode
     * @return
     * <AUTHOR>
     * @date 2018年12月5日 上午11:19:15
     */
    @MapKey("pmtDealNo")
    Map<String, PmtCheckAckVo> selectPaymentOrderForPayChkAck(@Param("sysCode") String sysCode, @Param("busiCode") String busiCode);
    /**
     * 
     * updateSysCode:更新syscode
     * @param sysCode
     * @return
     * <AUTHOR>
     * @date 2018年12月5日 上午10:47:55
     */
    int updateSysCode(String sysCode);

    /**
     *
     * countBankPaymentUnCheckOrderNum:统计当前TA未对账的订单数量
     *
     * @param taCode
     * @return
     * <AUTHOR>
     * @date 2019年9月17日 上午11:21:59
     */
    Integer countBankPaymentUnCheckOrderNum(String taCode);

    int deleteBySysCode(@Param("sysCode") String sysCode);

    void batchInsert(@Param("list") List<BankAcctPaymentCheckOrderPo> list);
}