/**
 * Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 * All right reserved.
 * <p>
 * THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 * MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 * TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 * WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.service.order.dealorder;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.ProductTypeEsSysCodeMappingEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.*;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.exception.DataBaseException;
import com.howbuy.tms.common.utils.CustBooksAmtOrVolUtils;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.MathUtils;
import com.howbuy.tms.high.batch.dao.po.batch.HighTradeExceptionPo;
import com.howbuy.tms.high.batch.dao.po.order.*;
import com.howbuy.tms.high.batch.dao.vo.HighRedeemSplitOrderErrorVo;
import com.howbuy.tms.high.batch.service.business.message.MsgNotifySendService;
import com.howbuy.tms.high.batch.service.business.redeemsplit.RedeemSplitContext;
import com.howbuy.tms.high.batch.service.event.HighEventPublisher;
import com.howbuy.tms.high.batch.service.event.payfail.PayFailEvent;
import com.howbuy.tms.high.batch.service.repository.*;
import com.howbuy.tms.high.batch.service.service.sequence.SequenceService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description:订单表处理service
 *
 * <AUTHOR>
 * @reason:
 * @date 2017年7月12日 上午9:33:25
 * @since JDK 1.7
 */
@Service("dealOrderService")
public class DealOrderService {

    private static final Logger logger = LogManager.getLogger(DealOrderService.class);
    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private SimuFundCheckOrderRepository simuFundCheckOrderRepository;
    @Autowired
    private CustBooksDtlRepository custBooksDtlRepository;
    @Autowired
    private EsProOrderMonitorRepository esProOrderMonitorRepository;
    @Autowired
    private CustBooksRepository custBooksRepository;
    @Autowired
    private SequenceService sequenceService;
    @Autowired
    private HighRedeemSplitOrderBackRepository highRedeemSplitOrderBackRepository;
    @Autowired
    private HighRedeemSplitOrderErrorRepository highRedeemSplitOrderErrorRepository;
    @Autowired
    private MsgNotifySendService msgNotifySendService;
    @Autowired
    private HighRedeemSplitDtlRepository highRedeemSplitDtlRepository;
    @Autowired
    private CustProtocolRepository custProtocolRepository;
    @Autowired
    private HighEventPublisher highEventPublisher;

    public int updateOrderStatOrPayStat(String dealNo, String orderStatus, String payStatus, Date oldUpdate, Date updateDate) {
        int num = dealOrderRepository.updateOrderStatOrPayStat(dealNo, orderStatus, payStatus, oldUpdate, updateDate);
        if (num > 0 && PayStatusEnum.PAY_FAIL.getCode().equals(payStatus)) {
            highEventPublisher.publishEvent(new PayFailEvent(dealNo));
        }
        return num;
    }


    public boolean execModifyVolAndResetSubmit(String dealNo, String dealDtlNo, String txAppFlag, String tradeExcpSrc, String txAcctNo, String esSysCode, BigDecimal modifyVol) {
        // 修改份额
        execModifyVol(dealDtlNo, dealNo, modifyVol);

        // 恢复重新上报
        execResetSubmit(dealNo, txAppFlag, tradeExcpSrc, txAcctNo, esSysCode);
        return true;
    }

    public boolean execModifyVol(String dealDtlNo, String dealNo, BigDecimal modifyVol) {
        CustBooksDtlPo custBooksDtlPo = new CustBooksDtlPo();
        custBooksDtlPo.setDealDtlNo(dealDtlNo);
        custBooksDtlPo.setAppVol(modifyVol);

        int updateCustBookDtlCount = custBooksDtlRepository.updateByDealDtlNoSelective(custBooksDtlPo);

        DealOrderPo dealOrderPo = new DealOrderPo();
        dealOrderPo.setDealNo(dealNo);
        dealOrderPo.setAppVol(modifyVol);
        int updateDealCount = dealOrderRepository.updateByDealNoSelective(dealOrderPo);

        HighDealOrderDtlPo highDealOrderDtlPo = new HighDealOrderDtlPo();
        highDealOrderDtlPo.setDealDtlNo(dealDtlNo);
        highDealOrderDtlPo.setAppVol(modifyVol);
        int updateHighDealCount = highDealOrderDtlRepository.updateByDealDtlNo(highDealOrderDtlPo);

        SimuFundCheckOrderPo simuFundCheckOrderPo = new SimuFundCheckOrderPo();
        simuFundCheckOrderPo.setDealDtlNo(dealDtlNo);
        simuFundCheckOrderPo.setAppVol(modifyVol);
        int updateSimuSubmitCount = simuFundCheckOrderRepository.updateBySubmitDealNoSelective(simuFundCheckOrderPo);

        if (updateDealCount != 1 || updateHighDealCount != 1 || updateSimuSubmitCount != 1) {
            logger.info("DealOrderService|execResetSubmit|dealNo:{},updateCustBookDtlCount:{},updateHighDealCount:{},updateDealCount:{},updateSimuSubmitCount:{}", dealNo, updateCustBookDtlCount, updateHighDealCount, updateDealCount, updateSimuSubmitCount);
            throw new BusinessException("", "修改份额失败");
        }

        return true;

    }

    /**
     * execReSubmit:(执行重新上报)
     *
     * @param dealNo       订单号
     * @param txAppFlag    原交易申请标识
     * @param tradeExcpSrc 交易异常来源 1-上报失败 2-额度不足 3-交易对账异常
     * @return
     * <AUTHOR>
     * @date 2017年11月14日 下午8:57:13
     */
    public boolean execResetSubmit(String dealNo, String txAppFlag, String tradeExcpSrc, String txAcctNo, String esSysCode) {
        logger.info("DealOrderService|execResetSubmit|dealno:{}, tradeExcpSrc: {}", dealNo, tradeExcpSrc);
        // 只有上报失败，和额度校验失败的撤单可以恢复重新上报
        if (!TradeExcpSrcEnum.SUBMIT_FAIL.getCode().equals(tradeExcpSrc) && !TradeExcpSrcEnum.LIMIT_CANCEL.getCode().equals(tradeExcpSrc)) {
            logger.info("DealOrderService|execResetSubmit|dealno:{}, tradeExcpSrc: {} is not  1-submitFail or 2-limit", dealNo, tradeExcpSrc);
            throw new BusinessException("", "该业务场景不支持重新上报");
        }

        String orderStatus = getOrderStatus(txAppFlag);

        if (null == orderStatus) {
            logger.info("DealOrderService|execResetSubmit|dealno:{}, txAppFlag: {} not in 0,2,3 ", dealNo, txAppFlag);
            throw new BusinessException("", "交易申请标识不满足");
        }
        Date now = new Date();

        // 设置订单订单状态：申请成功
        DealOrderPo dealOrderPo = new DealOrderPo();
        dealOrderPo.setOrderStatus(orderStatus);
        dealOrderPo.setMemo("op重新上报");
        dealOrderPo.setUpdateDtm(now);

        int updateDealCount = dealOrderRepository.updateSubmitFlagWithForce(dealNo, dealOrderPo);
        logger.info("DealOrderService|execResetSubmit|dealNo:{},updateDealCount:{}", dealNo, updateDealCount);
//        if (updateDealCount != 1) {
//            throw new BusinessException("", "更新订单状态失败");
//        }

        // 设置订单明细交易申请状态：申请成功
        HighDealOrderDtlPo highDealOrderDtlPo = new HighDealOrderDtlPo();
        highDealOrderDtlPo.setTxAppFlag(txAppFlag);
        highDealOrderDtlPo.setCancelOrderSrc("");
        highDealOrderDtlPo.setUpdateDtm(now);

        int updateDealDtlCount = highDealOrderDtlRepository.updateSubmitFlagWithForce(dealNo, highDealOrderDtlPo);
        logger.info("DealOrderService|execResetSubmit|dealNo:{},updateDealDtlCount:{}", dealNo, updateDealDtlCount);
//        if (updateDealDtlCount != 1) {
//            throw new BusinessException("", "更新订单明细状态失败");
//        }

        EsProOrderMonitorPo monitor = new EsProOrderMonitorPo();
        monitor.setDealNo(dealNo);
        monitor.setSysCode(esSysCode);
        monitor.setTxAcctNo(txAcctNo);
        esProOrderMonitorRepository.insertSelective(monitor);
        // 额度不足的情况还未进上报表，无需更新
        // 额度不足的情况还未生成cust_books_dtl记录，需要新增
        if (TradeExcpSrcEnum.LIMIT_CANCEL.getCode().equals(tradeExcpSrc)) {
            logger.info("DealOrderService|execResetSubmit|dealNo:{},tradeExcpSrc:{}", dealNo, tradeExcpSrc);
            List<CustBooksDtlPo> custBooksList = custBooksDtlRepository.selectByDealNo(dealNo);
            if (!CollectionUtils.isEmpty(custBooksList)) {
                logger.error("额度不足重新上报，已有在途明细，不再增加");
                return true;
            }
            addCustBooksDtl(dealNo, now);
            return true;
        }

        // 设置私募上报订单：申请成功,重新上报，未对账
        SimuFundCheckOrderPo simuFundCheckOrderPo = new SimuFundCheckOrderPo();
        simuFundCheckOrderPo.setCancelOrderSrc(null);
        simuFundCheckOrderPo.setTxAppFlag(txAppFlag);
        simuFundCheckOrderPo.setSubmitAppFlag(SubmitAppFlagEnum.SUBMIT_AGAIN.getCode());
        simuFundCheckOrderPo.setTxCompFlag(TxCompFlagEnum.NOT.getCode());
        simuFundCheckOrderPo.setRetCode("");
        simuFundCheckOrderPo.setRetDesc("");
        simuFundCheckOrderPo.setMemo("op重新上报");
        simuFundCheckOrderPo.setUpdateDtm(now);

        int updateSimuCheckCount = simuFundCheckOrderRepository.updateSubmitFlagWithForce(dealNo, simuFundCheckOrderPo);
        logger.info("DealOrderService|execResetSubmit|dealNo:{},updateSimuCheckCount:{}", dealNo, updateSimuCheckCount);
        if (updateSimuCheckCount > 1) {
            throw new BusinessException("", "更新上报状态失败");
        }

        if (TradeExcpSrcEnum.SUBMIT_FAIL.getCode().equals(tradeExcpSrc) && TxAppFlagEnum.APP_SUCCESS.getCode().equals(txAppFlag)) {

            List<CustBooksDtlPo> oldCustBooksList = custBooksDtlRepository.selectWithDrawByDealNo(dealNo);

            if (CollectionUtils.isEmpty(oldCustBooksList) || oldCustBooksList.size() < 1) {
                logger.info("DealOrderService|execResetSubmit|dealNo:{},oldCustBooksList size is null or not eq 1, size :{}", dealNo,
                        ((CollectionUtils.isEmpty(oldCustBooksList) ? 0 : oldCustBooksList.size())));
                return true;
            }
            logger.info("DealOrderService|execResetSubmit|dealno:{},oldCustBookDtl:{}", dealNo, JSON.toJSONString(oldCustBooksList.get(0)));

            // 删除账本明细
            int delBooksDtlNum = custBooksDtlRepository.deleteByCondition(dealNo);

            logger.info("DealOrderService|execResetSubmit|dealNo:{},delBooksDtlNum：{}", dealNo, delBooksDtlNum);
            if (delBooksDtlNum < 1) {
                throw new BusinessException("", "删除账本明细失败");
            }

        }

        return true;
    }

    /**
     * 额度不足，重新上报，需要新增custBooksDtl
     *
     * @param dealNo
     * @param now
     */
    private void addCustBooksDtl(String dealNo, Date now) {
        List<HighDealOrderDtlPo> highDealOrderDtlPos = highDealOrderDtlRepository.getByDealNo(dealNo);
        DealOrderPo dealOrderPo = dealOrderRepository.getByDealNo(dealNo);
        HighDealOrderDtlPo orderDtlPo = highDealOrderDtlPos.get(0);
        DealOrderPo orderPo = dealOrderRepository.getByDealNo(dealNo);
        // 新增账务变动明细
        CustBooksDtlPo bookPo = new CustBooksDtlPo();
        bookPo.setRecordNo(sequenceService.getCustBooksDtlNo(orderDtlPo.getTxAcctNo()));
        // 使用净申请金额
        bookPo.setAppAmt(orderDtlPo.getNetAppAmt());

        ChangeBusiCodeEnum chgBusiCode = ChangeBusiCodeEnum.PUR_APP;
        if (BusinessCodeEnum.SUBS.getMCode().equals(orderDtlPo.getmBusiCode())) {
            chgBusiCode = ChangeBusiCodeEnum.SUBS_APP;
        } else if (BusinessCodeEnum.FUND_SCHEDULE.getMCode().equals(orderDtlPo.getmBusiCode())) {
            chgBusiCode = ChangeBusiCodeEnum.FUND_SCHEDULE_APP;
        }
        bookPo.setChangeBusiCode(chgBusiCode.getCode());
        bookPo.setCpAcctNo(dealOrderPo.getCpAcctNo());
        bookPo.setDealDtlNo(orderDtlPo.getDealDtlNo());
        bookPo.setDealNo(orderDtlPo.getDealNo());
        bookPo.setDisCode(orderDtlPo.getDisCode());
        bookPo.setDisTxAcctNo(orderPo.getDisTxAcctNo());
        bookPo.setFundShareClass(orderDtlPo.getFundShareClass());
        bookPo.setProductCode(orderDtlPo.getFundCode());
        bookPo.setProductChannel(orderDtlPo.getProductChannel());
        bookPo.setProductName(orderDtlPo.getFundName());
        bookPo.setProductType(orderDtlPo.getFundType());
        String protocolNo = dealOrderPo.getProtocolNo();
        CustProtocolPo protocol = custProtocolRepository.getByProtocolNo(protocolNo);
        if (protocol != null) {
            bookPo.setProtocolType(protocol.getProtocolType());
        }
        bookPo.setProtocolNo(protocolNo);
        bookPo.setTaTradeDt(orderDtlPo.getSubmitTaDt());
        bookPo.setTradeDt(DateUtils.formatToString(now, DateUtils.YYYYMMDD));
        bookPo.setTradeTm(DateUtils.formatToString(now, DateUtils.HHMMSS));
        bookPo.setTxAcctNo(orderDtlPo.getTxAcctNo());
        bookPo.setTaCode(orderDtlPo.getTaCode());
        bookPo.setProductClass(orderDtlPo.getProductClass());// 产品类别

        int count = custBooksDtlRepository.insertSelective(bookPo);
        if (count != 1) {
            throw new BusinessException("", "新增在途明细错误");
        }
    }


    private String getOrderStatus(String txAppFlag) {

        if (TxAppFlagEnum.SELF_REVOCATION.getCode().equals(txAppFlag)) {
            return OrderStatusEnum.SELF_CANCELED.getCode();
        } else if (TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(txAppFlag)) {
            return OrderStatusEnum.FORCE_CANCELED.getCode();
        } else if (TxAppFlagEnum.APP_SUCCESS.getCode().equals(txAppFlag)) {
            return OrderStatusEnum.APP_SUCCESS.getCode();
        }

        return null;
    }

    /**
     * @param modifyVol
     * @return boolean
     * @Description 修改份额
     * <AUTHOR>
     * @Date 2020/4/3 10:42
     **/
    public boolean execModifyVolSubmit(HighTradeExceptionPo highTradeExceptionPo, BigDecimal modifyVol) {
        String dealNo = highTradeExceptionPo.getDealNo();
        String dealLDtlNo = highTradeExceptionPo.getDealDtlNo();

        DealOrderPo dealOrderPo = dealOrderRepository.getByDealNo(dealNo);
        if (dealOrderPo == null) {
            logger.info("execModifyVolSubmit|dealNo:{} 订单不存在", dealNo);
            return false;
        }

        HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlRepository.selectByDealDtlNo(dealLDtlNo);

        Date currDate = new Date();

        DealOrderPo updateDealOrderPo = new DealOrderPo();
        updateDealOrderPo.setDealNo(dealNo);
        updateDealOrderPo.setAppVol(modifyVol);
        updateDealOrderPo.setUpdateDtm(currDate);
        updateDealOrderPo.setMemo("op重新上报");
        updateDealOrderPo.setOrderStatus(OrderStatusEnum.APP_SUCCESS.getCode());

        int updateDealCount = dealOrderRepository.updateByDealNoSelective(updateDealOrderPo);
        logger.info("execModifyVolSubmit|dealNo:{} updateDealCount:{}", dealNo, updateDealCount);
        if (updateDealCount != 1) {
            throw new BusinessException("", "更新订单份额失败");
        }

        HighDealOrderDtlPo updateHighDtlPo = new HighDealOrderDtlPo();
        updateHighDtlPo.setDealDtlNo(dealLDtlNo);
        updateHighDtlPo.setAppVol(modifyVol);
        updateHighDtlPo.setUpdateDtm(currDate);
        updateHighDtlPo.setCancelOrderSrc(null);
        updateHighDtlPo.setTxAppFlag(TxAppFlagEnum.APP_SUCCESS.getCode());
        updateHighDtlPo.setForceRedeemFlag(YesOrNoEnum.YES.getCode());

        int updateDtlCount = highDealOrderDtlRepository.updateByDealDtlNo(updateHighDtlPo);
        logger.info("execModifyVolSubmit|dealNo:{} updateDealCount:{}", dealNo, updateDtlCount);
        if (updateDtlCount != 1) {
            throw new BusinessException("", "更新订单明细份额失败");
        }
        List<SimuFundCheckOrderPo> simuFundCheckOrderPoList = simuFundCheckOrderRepository.selectByDealDtlNo(dealLDtlNo);
        if (!CollectionUtils.isEmpty(simuFundCheckOrderPoList)) {
            SimuFundCheckOrderPo simuFundCheckOrderPo = simuFundCheckOrderPoList.get(0);
            SimuFundCheckOrderPo updateSimuFundCheckOrderPo = new SimuFundCheckOrderPo();
            updateSimuFundCheckOrderPo.setCancelOrderSrc("");
            updateSimuFundCheckOrderPo.setTxAppFlag(TxAppFlagEnum.APP_SUCCESS.getCode());
            updateSimuFundCheckOrderPo.setSubmitAppFlag(SubmitAppFlagEnum.SUBMIT_AGAIN.getCode());
            updateSimuFundCheckOrderPo.setTxCompFlag(TxCompFlagEnum.NOT.getCode());
            updateSimuFundCheckOrderPo.setAppVol(modifyVol);
            updateSimuFundCheckOrderPo.setRetCode("");
            updateSimuFundCheckOrderPo.setRetDesc("");
            updateSimuFundCheckOrderPo.setMemo("op修改份额重新上报");
            updateSimuFundCheckOrderPo.setUpdateDtm(currDate);
            updateSimuFundCheckOrderPo.setSubmitDealNo(simuFundCheckOrderPo.getSubmitDealNo());

            int updateSimuSubmitCount = simuFundCheckOrderRepository.updateBySubmitDealNoSelective(updateSimuFundCheckOrderPo);
            logger.info("execModifyVolSubmit|update SimuFundCheckOrder dealNo:{} updateSimuSubmitCount:{}", dealNo, updateSimuSubmitCount);
            if (updateSimuSubmitCount != 1) {
                throw new BusinessException("", "更新私募上报表份额失败");
            }
        }
        //1.账本明细没有归集到账本
        //1.1原订单申请成功，修改原订单的账本明细份额
        //1.2原订单强制取消，修改原订单账本明细份额，删除强制取消账本明细

        //2.账本明细归集到账本
        //2.1原订单申请成功，新增原订单冲正账本明细，新增修改后份额账本明细
        //2.2原订单强制取消，新增修改后份额账本明细
        List<CustBooksDtlPo> custBooksDtlList = custBooksDtlRepository.selectByDealNo(dealNo);
        CustBooksDtlPo custBooksDtlPoApp = getExistCustBookByBusiCode(custBooksDtlList, ChangeBusiCodeEnum.REEDEEM_APP.getCode(), highDealOrderDtlPo.getAppVol());
        CustBooksDtlPo custBooksDtlPoWithdrow = getExistCustBookByBusiCode(custBooksDtlList, ChangeBusiCodeEnum.REEDEEM_WITHDROW.getCode(), highDealOrderDtlPo.getAppVol());
        if (TxAppFlagEnum.FORCE_REVOCATION.getCode().equals(highDealOrderDtlPo.getTxAppFlag())) {
            // 1.存在强制取消订单，删除撤单账本明细
            // 2.存在申请成功账本明细，修改账本明细
            // 3.不存在申请成功账本明细，新增修改后份额账本明细
            if (custBooksDtlPoWithdrow != null) {
                int count = custBooksDtlRepository.deleteByRecordNo(custBooksDtlPoWithdrow.getRecordNo());
                logger.info("execModifyVolSubmit|delete custBookDtl recordNo:{} count:{}", custBooksDtlPoWithdrow.getRecordNo(), count);
            }

            if (custBooksDtlPoApp != null) {
                int count = updateModifyVol(modifyVol, custBooksDtlPoApp);
                logger.info("execModifyVolSubmit|update custBookDtl recordNo:{} count:{}", custBooksDtlPoApp.getRecordNo(), count);
            } else {
                CustBooksDtlPo custBooksDtlPoNew = buildCustBooksDtlPo(modifyVol, dealOrderPo, highDealOrderDtlPo, false);
                int count = custBooksDtlRepository.insertSelective(custBooksDtlPoNew);
                logger.info("execModifyVolSubmit|isnert custBookDtl recordNo:{} count:{}", custBooksDtlPoNew.getRecordNo(), count);
            }
        } else if (TxAppFlagEnum.APP_SUCCESS.getCode().equals(highDealOrderDtlPo.getTxAppFlag())) {
            // 1存在申请成功账本明细，修改账本明细
            // 2不存在申请成功账本明细，新增修改后份额账本明细，新增原订单份额冲正账本明细
            if (custBooksDtlPoApp != null) {
                int count = updateModifyVol(modifyVol, custBooksDtlPoApp);
                logger.info("execModifyVolSubmit|update custBookDtl recordNo:{} count:{}", custBooksDtlPoApp.getRecordNo(), count);
            } else {
                // 1.冲正原订单份额
                CustBooksDtlPo custBooksDtlPoForceCance = buildCustBooksDtlPo(highDealOrderDtlPo.getAppVol(), dealOrderPo, highDealOrderDtlPo, true);
                int count = custBooksDtlRepository.insertSelective(custBooksDtlPoForceCance);
                logger.info("execModifyVolSubmit|isnert custBookDtl recordNo:{} count:{}", custBooksDtlPoForceCance.getRecordNo(), count);
                // 2.新增修改后份额
                CustBooksDtlPo custBooksDtlPoNew = buildCustBooksDtlPo(modifyVol, dealOrderPo, highDealOrderDtlPo, false);
                count = custBooksDtlRepository.insertSelective(custBooksDtlPoNew);
                logger.info("execModifyVolSubmit|isnert custBookDtl recordNo:{} count:{}", custBooksDtlPoForceCance.getRecordNo(), count);

                // 3.主表减除原订单在途份额
                subtractBeforeBalanceVol(highDealOrderDtlPo);
            }
        }

        // 通知ES
        String esSysCode = ProductTypeEsSysCodeMappingEnum.getSysCode(ProductClassEnum.HIGH.getCode(), highDealOrderDtlPo.getFundType());
        EsProOrderMonitorPo monitor = new EsProOrderMonitorPo();
        monitor.setDealNo(dealNo);
        monitor.setSysCode(esSysCode);
        monitor.setTxAcctNo(dealOrderPo.getTxAcctNo());
        esProOrderMonitorRepository.insertSelective(monitor);

        // 通知crm
        msgNotifySendService.sendHighActualMessage(dealNo);

        return true;
    }

    private void subtractBeforeBalanceVol(HighDealOrderDtlPo highDealOrderDtlPo) {
        String txAcctNo = highDealOrderDtlPo.getTxAcctNo();
        String disCode = highDealOrderDtlPo.getDisCode();
        String cpAcctNo = highDealOrderDtlPo.getCpAcctNo();
        String productCode = highDealOrderDtlPo.getFundCode();
        String fundShareClass = highDealOrderDtlPo.getFundShareClass();
        String protocolType = highDealOrderDtlPo.getProtocolType();
        String protocolNo = highDealOrderDtlPo.getProtocolNo();

        CustBooksPo custBooksPo = custBooksRepository.getByUniqueKey(txAcctNo, disCode, cpAcctNo, productCode, fundShareClass, protocolType, protocolNo);
        if (custBooksPo != null &&
                custBooksPo.getUnconfirmedVol() != null &&
                custBooksPo.getUnconfirmedVol().compareTo(BigDecimal.ZERO) > 0) {

            CustBooksPo record = new CustBooksPo();
            record.setTxAcctNo(txAcctNo);
            record.setDisCode(disCode);
            record.setCpAcctNo(cpAcctNo);
            record.setProductCode(productCode);
            record.setFundShareClass(fundShareClass);
            record.setProtocolType(protocolType);
            record.setProtocolNo(protocolNo);
            record.setBalanceVol(custBooksPo.getUnconfirmedVol().subtract(highDealOrderDtlPo.getAppVol()));
            record.setUpdateDtm(new Date());
            custBooksRepository.updateByUniqueKeySelective(record);
            logger.info("update custBooks record:{}, before unconfirmedVol:{}", JSON.toJSONString(record), custBooksPo.getUnconfirmedAmt());
        }
    }

    private int updateModifyVol(BigDecimal modifyVol, CustBooksDtlPo custBooksDtlPoApp) {
        CustBooksDtlPo updateCustBooksDtlPo = new CustBooksDtlPo();
        updateCustBooksDtlPo.setRecordNo(custBooksDtlPoApp.getRecordNo());
        updateCustBooksDtlPo.setAppVol(MathUtils.multiply(new BigDecimal("-1"), modifyVol));
        return custBooksDtlRepository.updateByRecordNoSelective(updateCustBooksDtlPo);
    }

    private CustBooksDtlPo getExistCustBookByBusiCode(List<CustBooksDtlPo> custBooksDtlList, String changeBusiCode, BigDecimal appVol) {
        if (!CollectionUtils.isEmpty(custBooksDtlList)) {
            for (CustBooksDtlPo custBooksDtlPo : custBooksDtlList) {
                if (changeBusiCode.equals(custBooksDtlPo.getChangeBusiCode()) && custBooksDtlPo.getAppVol().abs().compareTo(appVol) == 0) {
                    return custBooksDtlPo;
                }
            }
        }

        return null;
    }


    private CustBooksDtlPo buildCustBooksDtlPo(BigDecimal modifyVol, DealOrderPo dealOrderPo, HighDealOrderDtlPo highDealOrderDtlPo, boolean isReverse) {
        CustBooksDtlPo custBooksDtlPoNew = new CustBooksDtlPo();
        Object[] objs = CustBooksAmtOrVolUtils.calculate(modifyVol, highDealOrderDtlPo.getmBusiCode(), isReverse);
        BigDecimal appVol = (BigDecimal) objs[0];
        ChangeBusiCodeEnum changeBusiCode = (ChangeBusiCodeEnum) objs[1];
        custBooksDtlPoNew.setAppVol(appVol);
        custBooksDtlPoNew.setChangeBusiCode(changeBusiCode.getCode());
        custBooksDtlPoNew.setRecordNo(sequenceService.getCustBooksDtlNo(highDealOrderDtlPo.getTxAcctNo()));
        custBooksDtlPoNew.setDealNo(highDealOrderDtlPo.getDealNo());
        custBooksDtlPoNew.setDealDtlNo(highDealOrderDtlPo.getDealDtlNo());
        custBooksDtlPoNew.setTxAcctNo(dealOrderPo.getTxAcctNo());
        custBooksDtlPoNew.setDisCode(dealOrderPo.getDisCode());
        custBooksDtlPoNew.setDisTxAcctNo(dealOrderPo.getDisTxAcctNo());
        custBooksDtlPoNew.setTradeDt(dealOrderPo.getAppDate());
        custBooksDtlPoNew.setTradeTm(dealOrderPo.getAppTime());
        custBooksDtlPoNew.setTaTradeDt(highDealOrderDtlPo.getSubmitTaDt());
        custBooksDtlPoNew.setProductType(highDealOrderDtlPo.getFundType());
        custBooksDtlPoNew.setProductCode(highDealOrderDtlPo.getFundCode());
        custBooksDtlPoNew.setFundShareClass(highDealOrderDtlPo.getFundShareClass());
        custBooksDtlPoNew.setProductName(highDealOrderDtlPo.getFundName());
        custBooksDtlPoNew.setProtocolNo(dealOrderPo.getProtocolNo());
        custBooksDtlPoNew.setProtocolType(dealOrderPo.getProtocolType());
        custBooksDtlPoNew.setCpAcctNo(dealOrderPo.getCpAcctNo());
        custBooksDtlPoNew.setProductClass(highDealOrderDtlPo.getProductClass());
        custBooksDtlPoNew.setTaCode(highDealOrderDtlPo.getTaCode());
        custBooksDtlPoNew.setProductChannel(highDealOrderDtlPo.getProductChannel());
        return custBooksDtlPoNew;
    }

    /**
     * @param highDealOrderDtlPo
     * @return void
     * @Description 修改份额强制取消
     * <AUTHOR>
     * @Date 2020/4/3 14:04
     **/
    public void execModifyVolForceCancel(HighDealOrderDtlPo highDealOrderDtlPo, DealOrderPo dealOrderPo) {
        String mBusiCode = highDealOrderDtlPo.getmBusiCode();
        if (!BusinessCodeEnum.REDEEM.getMCode().equals(mBusiCode)) {
            // 非赎回，不支持改份额
            return;
        }

        if (!TxAppFlagEnum.APP_SUCCESS.getCode().equals(highDealOrderDtlPo.getTxAppFlag())) {
            logger.info("execModifyVolForceCancel|dealNo:{}, txAppFlag:{}", highDealOrderDtlPo.getDealNo(), highDealOrderDtlPo.getTxAppFlag());
            return;
        }

        Date now = new Date();
        HighDealOrderDtlPo updateHighDtlPo = new HighDealOrderDtlPo();
        updateHighDtlPo.setDealDtlNo(highDealOrderDtlPo.getDealDtlNo());
        updateHighDtlPo.setUpdateDtm(now);
        updateHighDtlPo.setCancelOrderSrc(CancelOrderSrcEnum.MODIFYVOL_FORCE_CANCEL.getCode());
        updateHighDtlPo.setTxAppFlag(TxAppFlagEnum.FORCE_REVOCATION.getCode());
        updateHighDtlPo.setNotifySubmitFlag(NotifySubmitFlagEnum.NO_NEED.getCode());
        updateHighDtlPo.setForceRedeemFlag(YesOrNoEnum.YES.getCode());
        updateHighDtlPo.setMemo("修改份额强制取消");
        int affectNum = highDealOrderDtlRepository.updateByDealDtlNo(updateHighDtlPo);

        DealOrderPo updateDealOrderPo = new DealOrderPo();
        updateDealOrderPo.setDealNo(dealOrderPo.getDealNo());
        updateDealOrderPo.setOrderStatus(OrderStatusEnum.FORCE_CANCELED.getCode());
        updateDealOrderPo.setMemo("修改份额强制取消");
        updateDealOrderPo.setUpdateDtm(now);
        int updateDealCount = dealOrderRepository.updateOrderStatus(OrderStatusEnum.APP_SUCCESS.getCode(), dealOrderPo.getDealNo(), updateDealOrderPo);
        if (updateDealCount <= 0) {
            throw new DataBaseException("", "更新订单状态强制取消失败" + updateDealOrderPo.getDealNo());
        }

        List<SimuFundCheckOrderPo> simuFundCheckOrderPoList = simuFundCheckOrderRepository.selectByDealDtlNo(highDealOrderDtlPo.getDealDtlNo());
        if (!CollectionUtils.isEmpty(simuFundCheckOrderPoList)) {
            SimuFundCheckOrderPo simuFundCheckOrderPo = simuFundCheckOrderPoList.get(0);
            if (SubmitAppFlagEnum.SUBMITTED.getCode().equals(simuFundCheckOrderPo.getSubmitAppFlag())) {
                throw new BusinessException("", "订单：" + simuFundCheckOrderPo.getDealNo() + "已上报，不能强制撤单");
            }
            simuFundCheckOrderPo.setTxAppFlag(TxAppFlagEnum.FORCE_REVOCATION.getCode());

            // 上报标记=上报完成
            simuFundCheckOrderPo.setSubmitAppFlag(SubmitAppFlagEnum.NO_NEED_SUBMITTING.getCode());
            simuFundCheckOrderPo.setTxCompFlag(TxCompFlagEnum.NOT_NEED.getCode());
            // 撤单来源=强制取消
            simuFundCheckOrderPo.setCancelOrderSrc(CancelOrderSrcEnum.MODIFYVOL_FORCE_CANCEL.getCode());
            simuFundCheckOrderPo.setMemo("修改份额强制取消");
            // 更新时间
            simuFundCheckOrderPo.setUpdateDtm(now);
            simuFundCheckOrderRepository.updateBySubmitDealNoSelective(simuFundCheckOrderPo);
        }

        if (affectNum > 0) {
            // 新增账务变动明细
            CustBooksDtlPo bookPo = buildCustBooksDtlPo(highDealOrderDtlPo.getAppVol(), dealOrderPo, highDealOrderDtlPo, true);
            custBooksDtlRepository.insert(bookPo);
        }

        // 通知ES
        String esSysCode = ProductTypeEsSysCodeMappingEnum.getSysCode(ProductClassEnum.HIGH.getCode(), highDealOrderDtlPo.getFundType());
        EsProOrderMonitorPo monitor = new EsProOrderMonitorPo();
        monitor.setDealNo(dealOrderPo.getDealNo());
        monitor.setSysCode(esSysCode);
        monitor.setTxAcctNo(dealOrderPo.getTxAcctNo());
        esProOrderMonitorRepository.insertSelective(monitor);

        // 通知crm
        msgNotifySendService.sendHighActualMessage(dealOrderPo.getDealNo());
    }

    /**
     * 保存拆单明细
     *
     * @param dtlMap
     */
    public void saveRedeemSplitDtls(Map<String, RedeemSplitContext> dtlMap, String tradeDt) {
        dtlMap.values().forEach(context -> {
            List<HighRedeemSplitOrderErrorVo> subOrders = context.getSplitOrderDtls();
            // 子订单
            if (CollectionUtil.isNotEmpty(subOrders)) {
                // 转换订单
                List<HighRedeemSplitDtlPo> dtlPos = new ArrayList<>();
                for (HighRedeemSplitOrderErrorVo po : subOrders) {
                    HighRedeemSplitDtlPo dtlPo = buildDtlPo(po);
                    dtlPos.add(dtlPo);
                }
                // 插入拆单明细
                int dtlInsertRows = highRedeemSplitDtlRepository.batchInsert(dtlPos);
                logger.info("saveRedeemSplitDtls|dtlPos.size:{},dtlInsertRows:{}", dtlPos.size(), dtlInsertRows);
            }
            // 插入订单备份数据
            highRedeemSplitOrderBackRepository.insertSelective(context.getBackOrder());
        });
        // 删除异常表数据
        highRedeemSplitOrderErrorRepository.deleteByMainDealNos(dtlMap.keySet());
    }

    private HighRedeemSplitDtlPo buildDtlPo(HighRedeemSplitOrderErrorVo po) {
        HighRedeemSplitDtlPo dtlPo = new HighRedeemSplitDtlPo();
        dtlPo.setSplitDtlNo(po.getSplitDtlNo());
        dtlPo.setMainDealNo(po.getMainDealNo());
        dtlPo.setDealNo(po.getSubDealNo());
        dtlPo.setDealDtlNo(po.getSubDealDtlNo());
        dtlPo.setOriginSerialNo(po.getOriginSerialNo());
        dtlPo.setFundCode(po.getFundCode());
        dtlPo.setFundName(po.getFundName());
        dtlPo.setSubFundCode(po.getSubFundCode());
        dtlPo.setAckDt(po.getAckDt());
        dtlPo.setAppVol(po.getAppVol());
        dtlPo.setAckVol(po.getAckVol());
        dtlPo.setAckAmt(po.getAckAmt());
        dtlPo.setNav(po.getNav());
        dtlPo.setTxAcctNo(po.getTxAcctNo());
        dtlPo.setCpAcctNo(po.getCpAcctNo());
        dtlPo.setTxAckFlag("4");
        dtlPo.setFee(po.getFee());
        dtlPo.setJoinDt(po.getJoinDt());
        dtlPo.setCreateDtm(po.getCreateDtm());
        dtlPo.setUpdateDtm(po.getUpdateDtm());
        return dtlPo;
    }
}
