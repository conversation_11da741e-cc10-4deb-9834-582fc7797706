package com.howbuy.tms.high.batch.facade.enums;

/**
 * @Description:high-batch的tx_code
 * @Author: yun.lu
 * Date: 2025/7/17 15:27
 */
public class TxCodes {
    /**
     * 日初始化
     */
    public static final String HIGH_DAY_INIT = "Z900001";
    /**
     * 导入支付对账文件
     */
    public static final String HIGH_IMPORT_PAYMENT_CHECK_FILE = "Z920002";
    /**
     * 支付对账确认
     */
    public static final String HIGH_PAYMENT_CHECK_ACK = "Z900003";
    /**
     * 交易申请日终
     */
    public static final String HIGH_TRADE_APP_DAY_END = "Z900004";
    /**
     * 导入确认文件
     */
    public static final String HIGH_IMPORT_ACK_FILE = "Z900005";
    /**
     * 确认处理
     */
    public static final String HIGH_ACK_PROCESS = "Z900006";
    /**
     * 确认日终处理
     */
    public static final String HIGH_ACK_DAY_END_PROCESS = "Z900007";
    /**
     * 日终处理
     */
    public static final String HIGH_DAY_END_PROCESS = "Z900008";
    /**
     * 修改电子签名值
     */
    public static final String HIGH_MODIFY_ESIGNATURE = "Z900009";
    /**
     * 查询电子签名
     */
    public static final String HIGH_QUERY_ESIGNATURE = "Z900010";
    /**
     * 未审核柜台购买订单
     */
    public static final String HIGH_COUNTER_PURCHASE = "Z900011";
    /**
     * 未审核柜台赎回订单
     */
    public static final String HIGH_COUNTER_REDEEM = "Z900012";
    /**
     * 未审核柜台修改分红方式订单
     */
    public static final String HIGH_COUNTER_MODIFYDIV = "Z900013";
    /**
     * 未审核柜台撤单订单
     */
    public static final String HIGH_COUNTER_CANCEL = "Z900014";
    /**
     * 未审核柜台强制撤单订单
     */
    public static final String HIGH_COUNTER_FORCE_CANCEL = "Z900015";
    /**
     * 柜台审核
     */
    public static final String HIGH_COUNTER_CHECK = "Z900016";
    /**
     * 查询柜台订单
     */
    public static final String HIGH_QUERY_COUNTER_ORDER = "Z900017";
    /**
     * 柜台交易报表
     */
    public static final String HIGH_QUERY_COUNTER_REPORT = "Z900018";
    /**
     * 柜台收市
     */
    public static final String HIGH_COUNTER_END = "Z900019";
    /**
     * 查询高端上报/对账异常订单
     */
    public static final String HIGH_QUERY_FUND_CHECK_ORDER_DIFF = "Z900020";
    /**
     * 高端强制取消订单恢复重新上报
     */
    public static final String HIGH_RESET_SUBMIT = "Z900021";
    /**
     * 处理交易对账异常
     */
    public static final String HIGH_TRADE_CHECK_EXCP_PROC = "Z900022";
    /**
     * 高端份额确认书生成
     */
    public static final String HIGH_VOL_CONFIRM_BOOK = "Z900023";
    /**
     * 高端份额确认书查询
     */
    public static final String HIGH_QUERY_VOL_CONFIRM_BOOK = "Z900024";
    /**
     * 高端份额确认书T-1未生成数据统计
     */
    public static final String HIGH_UN_VOL_CONFIRM_BOOK = "Z900025";
    /**
     * 高端支付对账
     */
    public static final String HIGH_PAY_CHECK = "Z900026";
    /**
     * 高端交易异常查询
     */
    public static final String HIGH_QUERY_TRADE_EXCP = "Z900027";
    /**
     * 查询未干预订单
     */
    public static final String HIGH_QUERY_COMPL_INFO_LIST = "Z900028";
    /**
     * 更新合规信息
     */
    public static final String HIGH_UPDATE_COMPL_INFO = "Z900029";
    /**
     * 查询可干预的高端订单明细
     */
    public static final String HIGH_QUERY_DEAL_DTL_INTERPOSE = "Z900030";
    /**
     * 人工干预
     */
    public static final String HIGH_DEAL_DTL_INTERPOSE = "Z900031";
    /**
     * 人工上报
     */
    public static final String HIGH_QUERY_DEAL_SUBMIT = "Z900032";
    /**
     * 人工上报明细
     */
    public static final String HIGH_QUERY_DEAL_SUBMIT_DTL = "Z900033";
    /**
     * 子账本查询
     */
    public static final String HIGH_QUERY_SUB_CUST_BOOKS = "Z900034";
    /**
     * 到期赎回报表
     */
    public static final String HIGH_REPORT_EXPIRE_REDEEM = "Z900035";
    /**
     * 自定义组合相关代码
     */
    public static final String HIGH_EXPIRE_DATE_INTERPOSE = "Z900036";
    /**
     * 查询TA业务流程节点信息接口
     */
    public static final String HIGH_QUERY_TA_BUSINESS_BATCH_COUNT = "Z900037";
    /**
     * 重置TA业务流程节点信息接口
     */
    public static final String HIGH_RESET_TA_BUSINESS_BATCH_STAT = "Z900038";
    /**
     * 支付对账确认查询
     */
    public static final String HIGH_QUERY_PAYMENT_CHECK_ACK = "Z900039";
    /**
     * 查询批处理流程节点信息接口
     */
    public static final String HIGH_QUERY_BUSINESS_BATCH_FLOW = "Z900040";

    public static final String HIGH_QUERY_DAY_END_CHECK = "Z900041";
    /**
     * 查询电子签约
     */
    public static final String HIGH_QUERY_CUST_ESIGNATURE = "Z900042";
    /**
     * 查询系统工作日接口
     */
    public static final String HIGH_QUERY_WORKDAY = "Z900043";

    public static final String HIGH_QUERY_TA_BUSINESS_LIST = "Z900044";
    /**
     * 查询TA不收市信息接口
     */
    public static final String HIGH_QUERY_TA_COUNT_NOT_END = "Z900045";
    /**
     * 高端订单归集
     */
    public static final String HIGH_ORDER_COLLECT = "Z900046";
    /**
     * 高端交易订单明细查询 换卡
     */
    public static final String QUERY_GG_FUND_DEAL_ORDER_DTL_CHANGE_CARD = "Z900047";

    /**
     * 高端柜台修改复购协议
     */
    public static final String HIGH_COUNTER_MODIFY_REPURCHASEPROCTOL = "Z900048";

    /**
     * 高端查询未完成复购协议
     */
    public static final String HIGH_QUERY_UNFINISH_REPURCHASE_PROTOCOL = "Z900049";

    /**
     * 高端交易申请日终查询储蓄罐支付撤单异常单
     */
    public static final String HIGH_CXG_PAY_CANCEL_EX = "Z900050";

    /**
     * 高端交易申请日终查询储蓄罐支付存入对账异常单
     */
    public static final String HIGH_CXG_PAY_SAVE_EX = "Z900051";

    /**
     * 高端交易申请日终查询预约日历上报日变化，订单上报日未变化订单
     */
    public static final String HIGH_QUERY_SUBMIT_DT_UNCHANGE = "Z900052";

    /**
     * 高端干预预约支付日
     */
    public static final String HIGH_INTERPOSE_PMT_DT = "Z900053";

    /**
     * 未审核柜台非交易过户订单
     */
    public static final String HIGH_COUNTER_NOTRADE_OVERACCOUNT = "Z900054";

    /**
     * 上报股权产品认缴金额
     */
    public static final String SUBMIT_PE_SUBS_AMT = "Z900055";
    /**
     * 子账本查询
     */
    public static final String HIGH_QUERY_SUP_SIGN_CUST = "Z900056";
    /**
     * 添加线下补签协议
     */
    public static final String ADD_SUP_SIGN_OFFLINE = "Z900057";
    /**
     * 重新拆分赎回订单
     */
    public static final String RESPLIT_REDEEM_ORDERS = "Z900058";
    /**
     * 重新上报机构客户订单
     */
    public static final String RESUBMIT_HIGH_INST_ORDER = "Z900059";
    /**
     * 查询高端订单明细
     */
    public static final String QUERY_HIGH_DEAL_ORDER_DTL_RECON = "Z900060";
    /**
     * 分页查询全部高端订单明细
     */
    public static final String QUERY_HIGH_DEAL_ORDER_DTL_RECON_ALL = "Z900061";
    /**
     * 查询高端批处理流程节点TA子流程信息接口
     */
    public static final String QUERY_HIGH_TA_BATCH_FLOW_INFO = "Z900062";
    /**
     * 未审核柜台修改回款方向
     */
    public static final String HIGH_COUNTER_MODIFY_REFUND_DIRECTION = "Z900063";

    /**
     * 未审核柜台修改回款方向
     */
    public static final String HIGH_PARTNERS_NUMBER_UPDATE = "Z900064";

    /**
     * 股权份额转让业务code
     */
    public static final String OWNERSHIP_RIGHT_TRANSFER_TX_CODE = "Z900065";

    /**
     * 审核股权份额转让
     */
    public static final String COUNTER_OWNERSHIP = "Z900066";

    /**
     * 非交易修改认缴金额
     */
    public static final String NO_TRADE_UPDATE_SUBSCRIBE_AMT = "Z900067";

    /**
     * 修改认缴金额
     */
    public static final String UPDATE_SUBSCRIBE_AMT_APPLY = "Z900068";

    /**
     * 认缴金额修改审核
     */
    public static final String SUBSCRIBE_AMT_APPLY_CHECK = "Z900069";

    /**
     * 修改认缴金额详情
     */
    public static final String SUBSCRIBE_AMT_CHANGE_DETAIL = "Z900070";

    /**
     * 修改认缴金额申请单
     */
    public static final String SUBSCRIBE_AMT_APPLY_UPDATE = "Z900071";

    /**
     * 查询可干预的高端订单明细
     */
    public static final String QUERY_HIGH_MIDDLE_DEAL_INFO = "Z900072";

    /**
     * 查询预约单信息
     */
    public static final String QUERY_APPOINT_ORDER_INFO = "Z900073";

    /**
     * 查询高端TA收市状态
     */
    public static final String QUERY_HIGH_TA_DAY_CLOSE_STATUS = "Z900074";


























/******************************************公共的txCode***************/
    /** 交易对账接口 */
    public static final String BATCH_CENTER_TRADE_CHECK = "Z910002";
    /** 查询支付明细未对账订单 */
    public static final String QUERY_NOT_PMT_ORDER_DTL = "Z910010";
    /** 查询支付明细支付异常订单 */
    public static final String QUERY_PMT_FAILED_ORDER_DTL = "Z910011";
    /** 查询交易申请对账未对账交易订单 */
    public static final String QUERY_DEAL_APPLY_CHECK_NOT_PMT = "Z910012";
    /** 交易申请对账日终检查 */
    public static final String QUERY_DEAL_APPLY_CHECK_DAY_END = "Z910016";
    /** 勾稽-交易订单明细查询 */
    public static final String QUERY_GG_FUND_DEAL_ORDER_DTL = "Z910024";
    /** 勾稽-交易申请查询 */
    public static final String QUERY_GG_DEAL_APPLY = "Z910025";
    /** 勾稽-交易确认查询 */
    public static final String QUERY_GG_FUND_ACK_FILE = "Z910026";
    /** 查询客户余额信息 */
    public static final String QUERY_CUST_ACCOUNT_INFO = "Z910030";
    /** 查询电子合同 */
    public static final String QUERY_CUST_ECONTRACT = "Z910051";
    /** 电子合同签约接口 */
    public static final String SIGN_ESIGNATURE = "Z910053";
    /** 查询限额类型描述 */
    public static final String QUERY_LIMIT_TYPE_DES = "Z910056";
    /** 查询批处理流程状态 */
    public static final String QUERY_BATCH_FLOW_STAT = "Z910061";
    /** 柜台不收市TA */
    public static final String COUNTER_NOT_END_TA = "Z910082";
    /** 查询补签协议签署状态 */
    public static final String QUERY_AGREEMENT_SUP_SIGN_STATUS = "Z910108";
    /** 机构未申请日终的TA */
    public static final String QUERY_INST_NOT_APP_DAY_END_TA = "Z910115";
    /** 查询高端拆单明细 */
    public static final String QUERY_HIGH_SPLIT_DTL = "Z910128";
    /** 查询定投信息 */
    public static final String QUERY_HIGH_FUND_INV_PLAN = "Z330058";
    /** 查询定投明细信息 */
    public static final String QUERY_HIGH_FUND_INV_PLAN_DTL = "Z330059";
    /** 查询订单信息口 */
    public static final String QUERY_DEAL_INFO = "Z330077";
    /**
     * 分红
     */
    public static final String DIVIDEND = "Z310007";
    /**
     * 强增
     */
    public static final String STRONG = "Z310008";
    /**
     * 非交易过户
     */
    public static final String NOTRADE_OVERACCOUNT = "Z310064";
    /**
     * 基金转托管转入下单
     */
    public static final String TRANS_MANAGE_IN = "Z310049";
    /**
     * 份额转移
     */
    public static final String FUND_SHARE_DIVERT = "Z310072";
}
