---
description: 高端批处理中心项目级别规范指南 (High-Batch-Center Project Guidelines)
globs: ["**/*.java", "**/*.xml", "**/*.ftl", "**/*.properties", "**/*.yml", "**/*.yaml"]
alwaysApply: true
---

# 高端批处理中心项目级别规范指南 (High-Batch-Center Project Guidelines)

## 项目概述 (Project Overview)
高端批处理中心(high-batch-center)是负责处理好买金融高端基金产品批量交易处理的核心系统，包含批量处理、日终处理、对账、确认等功能。项目采用微服务架构，基于Spring Boot + Dubbo实现。

项目配置:
- 编程语言: Java
- JDK版本: 1.8
- 框架: Spring Boot, Spring Cloud, Dubbo
- 数据库: MySQL
- 缓存: Redis
- 数据库操作: MyBatis
- 数据库连接池: Druid
- 模板引擎: FreeMarker
- 文档生成: PDF生成、电子签名

### 模块划分 (Module Structure)
- **high-batch-center-client**: 包含Dubbo接口定义和出入参定义
  - 基础包名: `com.howbuy.tms.high.batch.facade`
  - 交易类接口: `com.howbuy.tms.high.batch.facade.trade.*`
  - 查询类接口: `com.howbuy.tms.high.batch.facade.query.*`
  - 枚举类: `com.howbuy.tms.high.batch.enums`
  - 接口入参: `com.howbuy.tms.high.batch.facade.*.request`
  - 接口出参: `com.howbuy.tms.high.batch.facade.*.response`

- **high-batch-center-dao**: 包含数据库操作和ORM相关配置
  - 基础包名: `com.howbuy.tms.high.batch.dao`
  - 数据库操作: `com.howbuy.tms.high.batch.dao.mapper`
  - 数据库实体: `com.howbuy.tms.high.batch.dao.po`
  - 业务对象: `com.howbuy.tms.high.batch.dao.vo`
  - 自定义Mapper: `com.howbuy.tms.high.batch.dao.mapper.customize`

- **high-batch-center-service**: 包含业务逻辑实现
  - 基础包名: `com.howbuy.tms.high.batch.service`
  - Dubbo接口实现: `com.howbuy.tms.high.batch.service.facade`
  - 业务实现: `com.howbuy.tms.high.batch.service.service`
  - 数据访问层: `com.howbuy.tms.high.batch.service.repository`
  - 业务处理: `com.howbuy.tms.high.batch.service.business`
  - 定时任务: `com.howbuy.tms.high.batch.service.job`
  - 文件处理: `com.howbuy.tms.high.batch.service.file`

## 命名规范 (Naming Conventions)

### 通用命名规则 (General Naming Rules)
- **类名**: 使用PascalCase，如`CounterEndService`、`HighDealOrderRepository`
- **方法名和变量名**: 使用camelCase，如`queryByTradeDt`、`dealAppNo`
- **常量**: 使用大写字母和下划线，如`MAX_RETRY_COUNT`、`DEFAULT_TIMEOUT`
- **包名**: 全小写，使用点分隔，如`com.howbuy.tms.high.batch.service`

### 特定组件命名规则 (Specific Component Naming Rules)

#### 接口和实体类
- **Dubbo接口**: 以`Facade`结尾，如`CounterEndFacade`、`QueryHighWorkdayFacade`
- **接口入参**: 接口名+`Request`，如`CounterEndRequest`
- **接口出参**: 接口名+`Response`，如`CounterEndResponse`
- **数据库实体**: 表名+`Po`，如`HighDealOrderDtlPo`、`CustEcontractPo`
- **业务对象**: 业务功能+`Vo`，如`CustInvestDealVo`、`PaymentOrderCountVo`
- **上下文对象**: 业务功能+`Context`，如`DigestSignContext`、`PdfFileExportContext`

#### 服务层组件
- **Dubbo实现类**: 接口名+`Impl`，如`CounterEndFacadeImpl`
- **Service类**: 业务功能+`Service`，如`GenerateCustInvestFileService`、`CmDataProcessService`
- **Repository类**: 表名+`Repository`，如`CmCustFundDirectRepository`
- **Mapper接口**: 表名+`Mapper`，如`CustEcontractPoMapper`

## 接口定义规范 (Interface Definition Standards)

### Dubbo接口定义
1. 接口必须继承`BaseFacade<Request, Response>`或相应的基类
2. 接口必须使用完整的JavaDoc注释
3. 所有方法必须有明确的业务含义描述

```java
/**
 * @description: 高端批处理接口示例
 * <AUTHOR>
 * @date 2025-01-XX XX:XX:XX
 * @since JDK 1.8
 */
public interface ExampleFacade extends BaseFacade<ExampleRequest, ExampleResponse> {
    /**
     * @description: 执行批处理业务
     * @param request 请求参数
     * @return 处理结果
     */
    Response<ExampleResponse> execute(ExampleRequest request);
}
```

### 入参/出参定义规范
1. 请求类必须继承`BaseRequest`、`BatchBaseRequest`或其他合适的基类
2. 响应类必须继承`BaseResponse`、`BatchBaseResponse`或其他合适的基类
3. 所有字段必须有注释说明用途
4. 使用标准的getter/setter方法或Lombok注解
5. 字段命名遵循Java驼峰命名法
6. 敏感字段应使用合适的序列化/反序列化策略

```java
/**
 * @description: 示例请求
 * <AUTHOR>
 * @date 2025-01-XX XX:XX:XX
 * @since JDK 1.8
 */
public class ExampleRequest extends BatchBaseRequest {
    private static final long serialVersionUID = 1L;

    /**
     * 交易日期
     */
    private String tradeDt;

    /**
     * TA代码列表
     */
    private List<String> taCodes;

    public ExampleRequest() {
        super.setTxCode(TxCodes.EXAMPLE_TX_CODE);
    }
    
    // getter和setter方法
}
```

## 接口实现规范 (Interface Implementation Standards)

### Dubbo接口实现类
1. 实现类必须位于`com.howbuy.tms.high.batch.service.facade`包下
2. 实现类命名为接口名+`Impl`
3. 使用`@DubboService`注解暴露服务
4. 通过`@Autowired`或`@Resource`注入所需的Service类
5. 方法实现应简洁，主要负责参数校验和Service层的调用

```java
@DubboService
@Slf4j
public class ExampleFacadeImpl implements ExampleFacade {
    @Autowired
    private ExampleService exampleService;
     
    @Override
    public Response<ExampleResponse> execute(ExampleRequest request) {
        try {
            return Response.ok(exampleService.process(request));
        } catch (BusinessException e) {
            log.error("业务处理异常: {}", e.getMessage(), e);
            return Response.fail(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("系统异常: {}", e.getMessage(), e);
            return Response.fail("SYSTEM_ERROR", "系统异常");
        }
    }
}
```

## 服务层调用规范 (Service Layer Standards)

### Service层
1. Service类应位于`com.howbuy.tms.high.batch.service.service`包下
2. 使用`@Service`注解将服务注册到Spring容器
3. 使用`@Slf4j`注解添加日志支持
4. 通过`@Autowired`注入Repository类和其他Service类
5. 实现具体业务逻辑，不直接操作数据库

```java
@Service
@Slf4j
public class ExampleService {
    @Autowired
    private ExampleRepository exampleRepository;
     
    /**
     * @description: 业务处理方法
     * @param request 请求参数
     * @return 处理结果
     * @author: developer.name
     * @date: 2025-01-XX XX:XX:XX
     * @since JDK 1.8
     */
    public ExampleResponse process(ExampleRequest request) {
        log.info("开始处理业务, 请求参数: {}", request);
        // 业务逻辑实现
        ExampleResponse response = new ExampleResponse();
        log.info("业务处理完成, 响应结果: {}", response);
        return response;
    }
}
```

### Repository层
1. Repository类应位于`com.howbuy.tms.high.batch.service.repository`包下
2. 使用`@Repository`注解将仓库注册到Spring容器
3. 使用`@Transactional`注解控制事务
4. 通过`@Autowired`注入Mapper接口
5. 实现数据库操作逻辑，不包含业务逻辑

```java
@Repository
@Slf4j
@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)
public class ExampleRepository {
    @Autowired
    private ExamplePoMapper exampleMapper;
     
    /**
     * @description: 查询数据
     * @param id 主键ID
     * @return 数据实体
     * @author: developer.name
     * @date: 2025-01-XX XX:XX:XX
     * @since JDK 1.8
     */
    public ExamplePo queryById(Long id) {
        return exampleMapper.selectByPrimaryKey(id);
    }
     
    /**
     * @description: 批量保存数据
     * @param entityList 数据实体列表
     * @return 影响行数
     * @author: developer.name
     * @date: 2025-01-XX XX:XX:XX
     * @since JDK 1.8
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public int batchInsert(List<ExamplePo> entityList) {
        return exampleMapper.batchInsert(entityList);
    }
}
```

## 业务处理规范 (Business Processing Standards)

### Business层
1. Business类应位于`com.howbuy.tms.high.batch.service.business`包下
2. 按业务功能划分子包，如`custesignprocess`、`custinvesthisfile`等
3. 复杂业务逻辑应封装在Business层，Service层调用Business层
4. 使用Context模式传递复杂参数

```java
@Component
@Slf4j
public class DigestSignBusinessService {
    @Autowired
    private DigestSignKeyOuterService digestSignKeyOuterService;
    
    /**
     * @description: 执行摘要签名业务
     * @param context 签名上下文
     * @author: developer.name
     * @date: 2025-01-XX XX:XX:XX
     * @since JDK 1.8
     */
    public void doSign(DigestSignContext context) {
        try {
            // 业务逻辑处理
            log.info("开始执行摘要签名, 客户号: {}", context.getCustInfoResult().getTxAcctNo());
            // 具体实现
        } catch (Exception e) {
            log.error("摘要签名处理异常: {}", e.getMessage(), e);
            throw new BusinessException("DIGEST_SIGN_ERROR", "摘要签名处理失败");
        }
    }
}
```

## 文件处理规范 (File Processing Standards)

### FreeMarker模板规范
1. 模板文件位于`src/main/resources/templates`目录下
2. 模板文件命名应具有业务含义，如`contractSignTemplate.ftl`
3. 模板中的变量命名使用camelCase
4. 模板应包含必要的HTML结构和CSS样式

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${title}</title>
    <style>
        .container { width: 960px; margin: 0 auto; }
        .header { text-align: center; font-weight: bold; }
    </style>
</head>
<body>
    <div class="container">
        <h3 class="header">${documentTitle}</h3>
        <p>客户姓名：${custName}</p>
        <p>证件号码：${idNo}</p>
        <!-- 其他内容 -->
    </div>
</body>
</html>
```

### PDF生成规范
1. PDF生成服务应位于`com.howbuy.tms.high.batch.service.file`包下
2. 使用统一的`PdfFileExportContext`传递参数
3. 生成的PDF文件应有唯一标识和版本控制

## 数据库操作规范 (Database Operation Standards)

### MyBatis Mapper规范
1. 自动生成的Mapper继承`*AutoMapper`
2. 自定义Mapper位于`customize`包下
3. SQL语句应使用参数化查询，防止SQL注入
4. 复杂查询应使用ResultMap映射结果

```xml
<!-- 批量插入示例 -->
<insert id="batchInsert" parameterType="java.util.List">
    INSERT INTO example_table (
        column1, column2, column3, create_time
    ) VALUES
    <foreach collection="list" item="item" separator=",">
        (#{item.column1}, #{item.column2}, #{item.column3}, NOW())
    </foreach>
</insert>

<!-- 条件查询示例 -->
<select id="queryByCondition" parameterType="map" resultMap="BaseResultMap">
    SELECT * FROM example_table
    WHERE 1=1
    <if test="param1 != null and param1 != ''">
        AND column1 = #{param1}
    </if>
    <if test="param2 != null">
        AND column2 = #{param2}
    </if>
</select>
```

## 异常处理规范 (Exception Handling Standards)

1. 使用统一的异常处理机制
2. 业务异常应继承自`BusinessException`
3. 系统异常应记录完整的堆栈信息
4. 异常信息应包含足够的上下文信息

```java
try {
    // 业务逻辑
    businessService.process(request);
} catch (BusinessException e) {
    log.error("业务处理异常, 请求: {}, 异常: {}", request, e.getMessage(), e);
    throw e;
} catch (Exception e) {
    log.error("系统异常, 请求: {}, 异常: {}", request, e.getMessage(), e);
    throw new BusinessException("SYSTEM_ERROR", "系统异常", e);
}
```

## 日志规范 (Logging Standards)

1. 使用SLF4J + Logback进行日志记录
2. 日志级别合理使用：ERROR、WARN、INFO、DEBUG
3. 日志内容应包含足够的上下文信息
4. 敏感信息不应记录到日志中，使用`PrivacyUtil`进行脱敏处理
5. 使用占位符而非字符串拼接

```java
// 正确的做法
log.info("处理订单, 订单号: {}, 客户号: {}", orderNo, PrivacyUtil.maskBankCardNo(txAcctNo));

// 错误的做法
log.info("处理订单, 订单号: " + orderNo + ", 客户号: " + txAcctNo);
```

### 日志上下文规范
1. 使用`CustomContextLookup`统一管理日志上下文
2. 重要业务操作应设置唯一的请求ID
3. 批处理任务应记录开始、进度和结束日志

## 事务管理规范 (Transaction Management Standards)

### 事务注解使用原则
1. Repository层方法默认使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.SUPPORTS)`
2. 数据修改操作使用`@Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)`
3. 查询操作不需要修改默认的事务传播行为
4. 批处理操作应考虑事务边界和性能影响

## 批处理特有规范 (Batch Processing Specific Standards)

### 批处理任务规范
1. 批处理任务应有明确的开始和结束日志
2. 批处理任务应有进度日志，便于监控
3. 批处理任务应有异常处理机制，避免因单条数据异常导致整个任务失败
4. 大批量数据处理应使用分页或分批处理

```java
@Component
@Slf4j
public class BatchProcessService {
    
    public void processBatch(String versionNo) {
        log.info("开始批处理, 版本号: {}", versionNo);
        int totalCount = 0;
        int successCount = 0;
        int failCount = 0;
        
        try {
            // 批处理逻辑
            List<DataPo> dataList = queryDataByVersion(versionNo);
            totalCount = dataList.size();
            
            for (DataPo data : dataList) {
                try {
                    processData(data);
                    successCount++;
                } catch (Exception e) {
                    log.error("处理数据异常, 数据ID: {}, 异常: {}", data.getId(), e.getMessage(), e);
                    failCount++;
                }
            }
        } finally {
            log.info("批处理完成, 版本号: {}, 总数: {}, 成功: {}, 失败: {}", 
                    versionNo, totalCount, successCount, failCount);
        }
    }
}
```

### 定时任务规范
1. 定时任务类应位于`com.howbuy.tms.high.batch.service.job`包下
2. 使用`@Scheduled`注解配置定时规则
3. 定时任务应有幂等性保证
4. 定时任务应有监控和告警机制

```java
@Component
@Slf4j
public class CheckCustomerIdCardHourExpireJob extends AbstractSimpleMessageJob {
    
    @Override
    protected void doProcessMessage(SimpleMessage message) {
        WorkdayPo workdayPo = workdayService.getWorkay(WorkdayTypeEnum.SYS_TYPE);
        String workDay = workdayPo.getWorkday();
        log.info("校验用户身份证是否过期,每小时提醒一次,校验当天上报的已支付订单,workDay={}", workDay);
        
        // 具体业务逻辑
    }
}
```

## 性能优化指南 (Performance Optimization Guidelines)

1. 合理使用索引提高查询性能
2. 避免N+1查询问题
3. 使用批量操作替代循环单条操作
4. 使用缓存减少数据库访问
5. 大数据量处理时使用分页查询
6. 合理设置连接池参数
7. 使用异步处理提高并发能力
8. 对于并行处理任务，考虑使用线程池和CompletableFuture

## 安全规范 (Security Standards)

1. 敏感数据（如密码、证件号）需要加密存储
2. API调用需要进行身份验证和授权
3. 防止SQL注入、XSS等常见安全问题
4. 日志中不应包含敏感信息，使用`PrivacyUtil`进行脱敏处理
5. 错误响应不应暴露系统内部信息

## 代码审查重点 (Code Review Focus)

1. **命名规范**: 类名、方法名、变量名是否符合规范
2. **接口定义**: Dubbo接口定义是否符合规范，包括注释、入参出参等
3. **接口实现**: 实现类是否位于正确的包下，是否使用了正确的注解
4. **服务层调用**: Service与Repository的职责是否分明，方法是否有必要的注释
5. **事务管理**: 是否正确使用了事务注解，事务传播行为是否合适
6. **异常处理**: 是否有统一的异常处理机制，是否合理使用了自定义异常
7. **日志规范**: 是否使用了正确的日志级别，日志内容是否合适，敏感信息是否脱敏
8. **性能考虑**: 是否有潜在的性能问题，如N+1查询、大事务等
9. **批处理规范**: 批处理任务是否有合适的错误处理和监控

## 常见问题处理 (Common Issue Handling)

### 并发控制
- 使用分布式锁控制并发访问
- 使用乐观锁处理数据更新冲突
- 关键业务操作使用悲观锁

### 幂等性处理
- 生成唯一标识符作为幂等性控制
- 使用状态检查防止重复操作
- 通过Redis等实现分布式锁或标记

### 数据一致性
- 关键业务使用事务保证数据一致性
- 跨系统调用使用补偿机制
- 定期进行数据校验和修复

## 附录 (Appendix)

### 业务错误码规范
- 0000: 成功
- A开头: 系统级错误
- B开头: 业务级错误
- C开头: 接口调用错误
- D开头: 数据错误

### 常用工具类
- `PrivacyUtil`: 敏感信息脱敏工具
- `DateUtil`: 日期处理工具
- `JsonUtil`: JSON序列化工具
- `FileUtil`: 文件操作工具

### 配置文件规范
- 配置文件应按环境分离
- 敏感配置应加密存储
- 配置变更应有版本控制

### 特殊业务处理规范
1. **储蓄罐对账处理**: 使用`CxgOrderCheckTask`进行金额和状态对账
2. **电子签名处理**: 使用`DigestSignContext`传递签名上下文，支持多种签名类型
3. **文件生成处理**: 使用FreeMarker模板生成PDF文件，支持高风险确认书、合同签署页等
4. **定时任务处理**: 继承`AbstractSimpleMessageJob`实现定时任务，确保幂等性
