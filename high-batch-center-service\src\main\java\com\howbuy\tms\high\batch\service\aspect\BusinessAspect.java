package com.howbuy.tms.high.batch.service.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Throwables;
import com.howbuy.tms.common.client.BaseRequest;
import com.howbuy.tms.common.client.BaseResponse;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.common.exception.DataBaseException;
import com.howbuy.tms.common.exception.SystemException;
import com.howbuy.tms.common.exception.ValidateException;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.common.utils.LogContextHelper;
import com.howbuy.tms.common.utils.ReflectUtil;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.common.OpsSysMonitor;
import com.howbuy.tms.high.batch.service.common.enums.CallDirectionEnum;
import com.howbuy.tms.high.batch.service.common.enums.CallTypeEnum;
import com.howbuy.tms.high.batch.service.common.utils.LoggerUtils;
import com.howbuy.trace.RequestChainTrace;
import org.apache.commons.lang.StringUtils;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.logging.log4j.ThreadContext;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
import java.util.Date;
import java.util.UUID;

/**
 * <AUTHOR>
 * @className BusinessAspect
 * @description 交易处理切面
 * @date 2015-3-23 下午5:24:31
 */
@Service("businessAspect")
class BusinessAspect {

    private static Logger logger = LogManager.getLogger(BusinessAspect.class);
    private static Logger mainLogger = LogManager.getLogger("mainlog");

    private static final SerializerFeature[] serializer = {SerializerFeature.WriteClassName, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty};

    public Object doAround(ProceedingJoinPoint pjp) {
        long start = System.currentTimeMillis();
        // 获取方法参数
        Object[] args = pjp.getArgs();
        BaseRequest request = null;

        Signature signature = pjp.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        // traceId
        String reqId = RequestChainTrace.getReqId();
        String remoteHost = RequestChainTrace.getRemoteHost();
        RequestChainTrace.buildAndSet(reqId, remoteHost);
        if (StringUtils.isEmpty(reqId)) {
            reqId = String.valueOf(UUID.randomUUID());
        }

        // RequestChainTrace.buildAndSet已经自动设置了SLF4J MDC
        // 我们只需要设置Log4j2 ThreadContext来支持${ctx:uuid}语法
        String currentRanNo = RequestChainTrace.getRanNo();
        ThreadContext.put("uuid", reqId);
        ThreadContext.put("ranNo", currentRanNo);
        setLogLevel(pjp);

        // 记录 交易码.log 的日志(request)
        if (args != null && args.length > 0) {
            request = (BaseRequest) args[0];
            if (request != null) {
                if (logger.isInfoEnabled()) {
                    logger.info("请求入参:"+JSON.toJSONString(request, serializer));
                }
            }
        }

        BaseResponse result = null;
        try {
            result = (BaseResponse) pjp.proceed();
        } catch (ValidateException e) {
            logger.error("ValidateException:", e);
            result = createResponse(method);
            result.setReturnCode(e.getErrorCode());
            result.setDescription(e.getErrorDesc());
        } catch (BusinessException e) {
            logger.error("BusinessException:", e);
            result = createResponse(method);
            result.setReturnCode(e.getErrorCode());
            result.setDescription(e.getErrorDesc());
        } catch (DataBaseException e) {
            logger.error("DataBaseException:", e);
            result = createResponse(method);
            result.setReturnCode(ExceptionCodes.BATCH_CENTER_DB_ERROR);
            result.setDescription(MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_DB_ERROR));
        } catch (SystemException e) {
            logger.error("SystemException:", e);
            result = createResponse(method);
            result.setReturnCode(e.getErrorCode());
            result.setDescription(e.getErrorDesc());
        } catch (Throwable e) {
            logger.error("Throwable:", e);
            result = createResponse(method);
            result.setReturnCode(ExceptionCodes.BATCH_CENTER_SYSTEM_ERROR);
            result.setDescription(MessageSource.getMessageByCode(ExceptionCodes.BATCH_CENTER_SYSTEM_ERROR));
            addSysExceptionLog(e);
        }
        result.setTxId(reqId);
        // 如果有异常码，但是没有异常描述，设置以上描述
        if (StringUtils.isNotEmpty(result.getReturnCode()) && StringUtils.isEmpty(result.getDescription())) {
            result.setDescription(MessageSource.getMessageByCode(result.getReturnCode()));
        }
        if (logger.isInfoEnabled()) {
            logger.info("请求结果:"+JSON.toJSONString(result, serializer));
        }
        // 记录main.log
        long time = System.currentTimeMillis() - start;
        if (mainLogger.isInfoEnabled()) {
            String returnCode = result.getReturnCode();
            if (request != null) {
                mainLogger.info(convertMainLogV2(reqId, getTimeStr(), returnCode, time, remoteHost, signature.toShortString()));
            }
        }

        // 清理ThreadContext，MDC由RequestChainTrace.remove()自动清理
        try {
            ThreadContext.remove("uuid");
            ThreadContext.remove("ranNo");
        } catch (Exception e) {
            // 忽略清理异常
        }

        return result;
    }
    /**
     * 添加非业务异常日志
     */
    private void addSysExceptionLog(Throwable e) {
        String stackTrace = Throwables.getStackTraceAsString(e);
        if (stackTrace.length() > 200) {
            stackTrace = stackTrace.substring(0, 200);
        }
        OpsSysMonitor.warn("非业务异常" + stackTrace, OpsSysMonitor.ERROR);
    }
    private BaseResponse createResponse(Method method) {
        BaseResponse response = null;
        try {
            Type returnType = method.getGenericReturnType();
            Class<?> clazz = null;
            
            if (returnType instanceof Class<?>) {
                // 普通类类型，如 UserResponse.class
                clazz = (Class<?>) returnType;
            } else if (returnType instanceof java.lang.reflect.ParameterizedType) {
                // 泛型类型，如 List<String>，获取原始类型
                java.lang.reflect.ParameterizedType parameterizedType = (java.lang.reflect.ParameterizedType) returnType;
                Type rawType = parameterizedType.getRawType();
                if (rawType instanceof Class<?>) {
                    clazz = (Class<?>) rawType;
                }
            }
            
            // 确保类是BaseResponse的子类，保证安全性
            if (clazz != null && BaseResponse.class.isAssignableFrom(clazz)) {
                response = (BaseResponse) clazz.getDeclaredConstructor().newInstance();
            } else {
                // 如果不是BaseResponse子类或无法确定类型，回退到默认实现
                response = new BaseResponse();
            }
        } catch (Exception e) {
            response = new BaseResponse();
        }

        return response;
    }

    /**
     * 打印mainlog
     */
    private String convertMainLogV2(String traceId, String dateStr, String returnCode, long costTime, String remoteHost, String serviceName) {
        JSONObject logJson = new JSONObject();
        // 日志时间
        logJson.put("time", dateStr);
        // 客户端标识
        logJson.put("traceId", StringUtils.defaultString(traceId));
        // 调用随机号
        logJson.put("ranNo", StringUtils.defaultString(LoggerUtils.getRanNo()));
        // 远端IP
        logJson.put("remoteIp", remoteHost);
        // 远端应用名称
        logJson.put("remoteName", RequestChainTrace.getRemoteApplicationName());
        // 接口名称
        logJson.put("serviceName", StringUtils.defaultString(serviceName));
        // 返回码
        logJson.put("returnCode", StringUtils.defaultString(returnCode));
        // 耗时
        logJson.put("cost", costTime);
        // 调用类型（dubbo、mq、http）
        logJson.put("callType", CallTypeEnum.DUBBO.getType());
        // 调用方向（in、out）
        logJson.put("direction", CallDirectionEnum.IN);
        return logJson.toJSONString();
    }

    /**
     * @description: 获取调用者服务名称
     */
    public static String getHttpCallerServiceName() {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (attributes == null) {
            return null;
        }
        HttpServletRequest request = attributes.getRequest();
        return request.getHeader("Caller-Service");
    }

    private String convertMainLog(String traceId, String dateStr, String txCode, String returnCode, long costTime, String remoteHost) {
        JSONObject json = new JSONObject();
        json.put("time", dateStr);
        json.put("operation", "00");
        json.put("tid", traceId);
        json.put("tx_code", txCode);
        json.put("return_code", returnCode);
        json.put("costs", costTime);
        json.put("remoteHost", remoteHost);
        return json.toJSONString();
    }

    private String getTimeStr() {
        Date now = new Date();
        return DateUtils.formatToString(now, "HH:mm:ss");
    }

    /**
     * 请求打印debug日志，辅助查询
     */
    private void setLogLevel(ProceedingJoinPoint joinPoint) {
        try {
            for (Object obj : joinPoint.getArgs()) {
                if (obj == null) {
                    continue;
                }
                Object logLevel = ReflectUtil.getValue(obj, "logLevel");
                if (!org.springframework.util.StringUtils.isEmpty(logLevel)) {
                    logger.info("setLogLevel:{}", logLevel);
                    RequestChainTrace.setTraceContext(LogContextHelper.LOG_LEVEL_KEY, logLevel);
                }
            }
        } catch (Exception e) {
            logger.error("setLogLevel error:{}", e.getMessage());
        }
    }

}