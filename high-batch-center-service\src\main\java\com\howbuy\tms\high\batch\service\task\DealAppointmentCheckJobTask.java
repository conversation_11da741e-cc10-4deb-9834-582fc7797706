package com.howbuy.tms.high.batch.service.task;

import com.howbuy.interlayer.product.enums.portfolio.BusinessCodeEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.bean.HighProductInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.high.batch.dao.vo.WaitSubmitOrderDto;
import com.howbuy.tms.high.batch.service.common.OpsSysMonitor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * @Description:校验预约日历是否有效任务
 * @Author: yun.lu
 * Date: 2024/1/23 9:30
 */
public class DealAppointmentCheckJobTask implements Runnable {
    private static Logger logger = LogManager.getLogger(DealAppointmentCheckJobTask.class);
    /**
     * 查询产品信息类
     */
    private QueryHighProductOuterService queryHighProductOuterService;
    /**
     * 待提交订单列表
     */
    private List<WaitSubmitOrderDto> waitSubmitOrderDtoList;

    /**
     * 计数栅
     */
    private CountDownLatch latch;

    public DealAppointmentCheckJobTask(QueryHighProductOuterService queryHighProductOuterService, List<WaitSubmitOrderDto> waitSubmitOrderDtoList, CountDownLatch latch) {
        this.queryHighProductOuterService = queryHighProductOuterService;
        this.waitSubmitOrderDtoList = waitSubmitOrderDtoList;
        this.latch = latch;
    }

    @Override
    public void run() {
        try {
            for (WaitSubmitOrderDto waitSubmitOrderDto : waitSubmitOrderDtoList) {
                HighProductInfoBean highProductInfoBean = queryHighProductOuterService.getHighProductInfo(waitSubmitOrderDto.getFundCode());
                String isScheduledTrade = highProductInfoBean.getIsScheduledTrade();
                if (isScheduledTrade(isScheduledTrade, waitSubmitOrderDto.getMBusiCode())) {
                    String busiType = BusinessCodeEnum.REDEEM.getMCode().equals(waitSubmitOrderDto.getMBusiCode()) ? "1" : "0";
                    ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(
                            highProductInfoBean.getFundCode(), busiType, highProductInfoBean.getShareClass(), waitSubmitOrderDto.getDisCode(), waitSubmitOrderDto.getAppDate());
                    if (productAppointmentInfoBean == null) {
                        String msg = waitSubmitOrderDto.getFundCode() + "-" + waitSubmitOrderDto.getFundName() + ",客户:" + waitSubmitOrderDto.getCustName() + "-" + waitSubmitOrderDto.getTxAcctNo() + ",订单号:" + waitSubmitOrderDto.getDealNo() + ",未匹配到预约期，请smop核对";
                        OpsSysMonitor.businessWarn(msg, OpsSysMonitor.ERROR);
                        logger.error("{}",msg);
                    }
                }
            }
        } catch (Exception e) {
            logger.info("AppointmentCheckJobTask-校验预约日历是否有效任务-出现异常,e:" + e);
        } finally {
            latch.countDown();
        }

    }


    private boolean isScheduledTrade(String isScheduledTrade, String mBusinessCode) {
        if (BusinessCodeEnum.SUBS.getMCode().equals(mBusinessCode)) {
            return true;
        }
        if (BusinessCodeEnum.PURCHASE.getMCode().equals(mBusinessCode)) {
            return IsScheduledTradeEnum.SupportBuyAdvance.getCode().equals(isScheduledTrade)
                    || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(isScheduledTrade);
        } else if (BusinessCodeEnum.REDEEM.getMCode().equals(mBusinessCode)) {
            return IsScheduledTradeEnum.SupportRedeemAdvance.getCode().equals(isScheduledTrade)
                    || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(isScheduledTrade);
        }
        return false;

    }
}
