package com.howbuy.tms.high.batch.dao.mapper.customize.batch;

import com.howbuy.tms.high.batch.dao.mapper.batch.CxgPaymentCheckOrderPoAutoMapper;
import com.howbuy.tms.high.batch.dao.po.batch.CxgPaymentCheckOrderPo;
import com.howbuy.tms.high.batch.dao.vo.PaymentOrderCountVo;
import com.howbuy.tms.high.batch.dao.vo.PmtCheckAckVo;

import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;

/**
 * 去O
 */
public interface CxgPaymentCheckOrderPoMapper extends CxgPaymentCheckOrderPoAutoMapper {

    /**
     * 
     * deleteCXGPaymentCheckOrder:清理储蓄罐支付对账数据
     * 
     * @return
     * <AUTHOR>
     * @date 2016年10月12日 下午8:18:15
     */
    int deleteCXGPaymentCheckOrder(@Param("sysCode") String sysCode);

    /**
     * 
     * insertCXGPaymentCheckOrderFromTempTable:从临时表插入储蓄罐支付对账订单
     * 
     * @return
     * <AUTHOR>
     * @date 2016年10月12日 下午8:19:36
     */
    int insertCXGPaymentCheckOrderFromTempTable(@Param("sysCode") String sysCode, @Param("tableName") String tableName);

    /**
     * selectTotalOrderNumAndTotalAmt:(储蓄罐支付订单统计)
     * 
     * @return
     * <AUTHOR>
     * @date 2016年10月13日 下午9:37:17
     */
    PaymentOrderCountVo selectTotalOrderNumAndTotalAmt(@Param("sysCode") String sysCode);
    /**
     * 
     * selectPaymentOrderForPayChkAck:支付对账确认
     * 
     * @param sysCode
     * @return
     * <AUTHOR>
     * @date 2018年12月5日 上午11:18:35
     */
    @MapKey("pmtDealNo")
    Map<String, PmtCheckAckVo> selectPaymentOrderForPayChkAck(@Param("sysCode") String sysCode);
    
    /**
     * 
     * updateSysCode:更新syscode
     * @param sysCode
     * @return
     * <AUTHOR>
     * @date 2018年12月5日 上午10:47:55
     */
    int updateSysCode(String sysCode);

    int deleteBySysCode(@Param("sysCode") String sysCode);

    void batchInsert(@Param("list") List<CxgPaymentCheckOrderPo> list);
}