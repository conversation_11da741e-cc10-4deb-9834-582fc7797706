package com.howbuy.tms.high.batch.service.config;

import com.howbuy.tms.high.batch.facade.common.Constant;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Author: yun.lu
 * Date: 2023/5/23 14:11
 */
@Configuration
public class ThreadPoolConfig {

    @Bean(name = Constant.HIGH_BATCH_DEFAULT_POOL)
    @Primary
    public ThreadPoolTaskExecutor threadPoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cpuCoreNum + 1);
        executor.setMaxPoolSize(2 * cpuCoreNum);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(10);
        executor.setThreadNamePrefix(Constant.HIGH_BATCH_DEFAULT_POOL);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.initialize();
        return executor;
    }


    @Bean(name = Constant.HIGH_BATCH_DEFAULT_EVENT_POOL)
    public ThreadPoolTaskExecutor eventPoolExecutor() {
        int cpuCoreNum = Runtime.getRuntime().availableProcessors();
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(cpuCoreNum + 1);
        executor.setMaxPoolSize(2 * cpuCoreNum);
        executor.setQueueCapacity(500);
        executor.setKeepAliveSeconds(10);
        executor.setThreadNamePrefix(Constant.HIGH_BATCH_DEFAULT_EVENT_POOL);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(ThreadTraceHelper::decorate);
        executor.initialize();
        return executor;
    }
}
