package com.howbuy.tms.high.batch.service.repository;

import com.howbuy.tms.high.batch.dao.mapper.customize.batch.BankAcctPaymentCheckOrderPoMapper;
import com.howbuy.tms.high.batch.dao.mapper.customize.batch.CxgPaymentCheckOrderPoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class ImportPayChkRepository {
    @Autowired
    private BankAcctPaymentCheckOrderPoMapper bankAcctPaymentCheckOrderPoMapper;

    @Autowired
    private CxgPaymentCheckOrderPoMapper cxgPaymentCheckOrderPoMapper;


    /**
     * deletePaymentData:删除导入数据
     *
     * @param sysCode
     * <AUTHOR>
     * @date 2018年12月5日 上午11:00:33
     */
    public void deletePaymentData(String sysCode) {
        int countCXG = cxgPaymentCheckOrderPoMapper.deleteCXGPaymentCheckOrder(sysCode);
        log.info("ImportPayChkService|deletePaymentData|sysCode:{},countCXG:{}", sysCode, countCXG);
        int countBank = bankAcctPaymentCheckOrderPoMapper.deleteBankAcctPaymentCheckOrder(sysCode);
        log.info("ImportPayChkService|deletePaymentData|sysCode:{},countBank:{}", sysCode, countBank);
    }

    /**
     * updateSysCode:更新系统码
     *
     * @param sysCode
     * <AUTHOR>
     * @date 2018年12月5日 上午11:00:44
     */
    public void updateSysCode(String sysCode) {
        int countCXG = cxgPaymentCheckOrderPoMapper.updateSysCode(sysCode);
        log.info("ImportPayChkService|updateSysCode|countCXG:{}", countCXG);
        int countBank = bankAcctPaymentCheckOrderPoMapper.updateSysCode(sysCode);
        log.info("ImportPayChkService|updateSysCode|countBank:{}", countBank);
    }
}
