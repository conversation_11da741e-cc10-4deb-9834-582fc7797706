package com.howbuy.tms.high.batch.service.job;

import com.alibaba.fastjson.JSON;
import com.howbuy.interlayer.product.model.HighProductBaseInfoModel;
import com.howbuy.interlayer.product.model.fund.ProductAppointmentInfoModel;
import com.howbuy.interlayer.product.model.fund.queryfundinfo.FundFeeRateModel;
import com.howbuy.interlayer.product.service.HighProductService;
import com.howbuy.interlayer.product.service.ProductAppointmentInfoService;
import com.howbuy.interlayer.product.service.fund.QueryFundInfoService;
import com.howbuy.message.SimpleMessage;
import com.howbuy.tms.common.enums.busi.BusinessCodeEnum;
import com.howbuy.tms.common.enums.busi.YesOrNoEnum;
import com.howbuy.tms.common.enums.database.IsScheduledTradeEnum;
import com.howbuy.tms.common.outerservice.interlayer.queryhighproduct.QueryHighProductOuterService;
import com.howbuy.tms.common.outerservice.interlayer.queryproductinfo.bean.ProductAppointmentInfoBean;
import com.howbuy.tms.common.outerservice.interlayer.querytradeday.QueryTradeDayOuterService;
import com.howbuy.tms.common.utils.DateUtils;
import com.howbuy.tms.high.batch.dao.po.order.HighDealOrderDtlPo;
import com.howbuy.tms.high.batch.dao.po.order.RedeemFeeNoticePo;
import com.howbuy.tms.high.batch.dao.po.order.SubCustBooksPo;
import com.howbuy.tms.high.batch.dao.vo.RedeemNoticeOrderInfoVo;
import com.howbuy.tms.high.batch.service.business.BatchMessageProcessor;
import com.howbuy.tms.high.batch.service.common.OpsSysMonitor;
import com.howbuy.tms.high.batch.service.common.enums.EstimateDataSourceEnum;
import com.howbuy.tms.high.batch.service.common.enums.RedeemFeeNoticeStatusEnum;
import com.howbuy.tms.high.batch.service.config.mq.MessageQueueConfig;
import com.howbuy.tms.high.batch.service.job.bean.RedeemFeeDetailDto;
import com.howbuy.tms.high.batch.service.repository.DealOrderRepository;
import com.howbuy.tms.high.batch.service.repository.HighDealOrderDtlRepository;
import com.howbuy.tms.high.batch.service.repository.RedeemFeeNoticeRepository;
import com.howbuy.tms.high.batch.service.repository.SubCustBooksRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:赎回费预警定时任务
 * @Author: yun.lu
 * Date: 2025/6/6 15:37
 */
@Service
@Slf4j
public class RedeemFeeNoticeJob extends BatchMessageProcessor {
    @Autowired
    private DealOrderRepository dealOrderRepository;
    @Autowired
    private RedeemFeeNoticeRepository redeemFeeNoticeRepository;
    @Autowired
    private QueryFundInfoService queryFundInfoService;
    @Autowired
    private HighProductService highProductService;
    @Autowired
    private QueryTradeDayOuterService queryTradeDayOuterService;
    @Autowired
    private QueryHighProductOuterService queryHighProductOuterService;
    @Autowired
    private ProductAppointmentInfoService productAppointmentInfoService;
    @Autowired
    private SubCustBooksRepository subCustBooksRepository;
    @Autowired
    private HighDealOrderDtlRepository highDealOrderDtlRepository;
    @Autowired
    private MessageQueueConfig messageQueueConfig;

    @Override
    protected String getQuartMessageChannel() {
        return messageQueueConfig.getRedeemFeeNoticeJobQueue();
    }

    @Override
    protected void doProcessMessage(SimpleMessage message) {
        // 1.查询交易日期是当天的订单
        String currentDt = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        List<RedeemNoticeOrderInfoVo> redeemOrderList = dealOrderRepository.queryRedeemOrderByApplyDt(currentDt);
        if (CollectionUtils.isEmpty(redeemOrderList)) {
            log.info("没有需要添加的赎回预警信息,currentDt={}", currentDt);
            return;
        }
        // 2.遍历订单
        List<RedeemFeeNoticePo> newRedeemFeeNoticeInfoList = buildRedeemFeeNoticeList(currentDt, redeemOrderList);
        if (CollectionUtils.isEmpty(newRedeemFeeNoticeInfoList)) {
            log.info("没有需要添加的赎回预警信息,currentDt={}", currentDt);
            return;
        }
        // 3.预警信息数据持久化
        redeemFeeNoticeRepository.batchInsert(newRedeemFeeNoticeInfoList);
        // 4.预警是否需要存在,以及告警判断
        errorNotice(currentDt, newRedeemFeeNoticeInfoList);
    }

    /**
     * 告警
     */
    private void errorNotice(String currentDt, List<RedeemFeeNoticePo> newRedeemFeeNoticeInfoList) {
        List<RedeemFeeNoticePo> needNoticeList = newRedeemFeeNoticeInfoList.stream().filter(redeemFeeNoticePo -> RedeemFeeNoticeStatusEnum.NOT_NOTICE.getCode().equals(redeemFeeNoticePo.getNoticeStatus())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(needNoticeList)) {
            log.info("没有需要告警的信息,currentDt={}", currentDt);
            return;
        }
        // 按照告警类型分组
        List<Long> idList = new ArrayList<>();
        for (RedeemFeeNoticePo redeemFeeNoticePo : needNoticeList) {
            OpsSysMonitor.businessWarn("基金代码:" + redeemFeeNoticePo.getFundCode() + ",基金简称:" + redeemFeeNoticePo.getFundName() + ",客户号:" + redeemFeeNoticePo.getTxAcctNo() + ",数据来源:" + redeemFeeNoticePo.getEstimateDataSource() + ",本期赎回和下一期赎回的费率可能不同", OpsSysMonitor.WARN);
            idList.add(redeemFeeNoticePo.getId());
        }
        redeemFeeNoticeRepository.updateBatchNoticeStatus(idList, RedeemFeeNoticeStatusEnum.NOTICE_SUCCESS.getCode());
    }

    /**
     * 订单赎回撤单导致的赎回费变更预警变更逻辑
     *
     * @param dealNo 订单号
     */
    public void cancelOrderRedeemFeeChangeNotice(String dealNo) {
        log.info("cancelOrderRedeemFeeChangeNotice-订单撤单导致的赎回费变更预警,dealNo={}", dealNo);
        if (StringUtils.isBlank(dealNo)) {
            log.error("cancelOrderRedeemFeeChangeNotice-订单撤单的赎回费变更预警,dealNo为空");
            return;
        }
        // 1.查询订单
        List<HighDealOrderDtlPo> highDealOrderDtlPoList = highDealOrderDtlRepository.getByDealNo(dealNo);
        if (CollectionUtils.isEmpty(highDealOrderDtlPoList)) {
            log.error("cancelOrderRedeemFeeChangeNotice-根据订单号查询不到订单,dealNo={}", dealNo);
            return;
        }
        HighDealOrderDtlPo highDealOrderDtlPo = highDealOrderDtlPoList.get(0);
        if (!BusinessCodeEnum.REDEEM.getMCode().equals(highDealOrderDtlPo.getmBusiCode())) {
            log.error("cancelOrderRedeemFeeChangeNotice-不是赎回单,不需要处理,dealNo={}", dealNo);
            return;
        }
        // 告警信息逻辑删除
        redeemFeeNoticeRepository.deleteByDealNo(dealNo);
        // 2.将该客户的所有未确认的申请成功的赎回订单按照申请时间从小到大排序
        List<RedeemNoticeOrderInfoVo> redeemOrderList = dealOrderRepository.selectUnConfrimRedeemOrder(highDealOrderDtlPo.getTxAcctNo(), highDealOrderDtlPo.getFundCode());
        redeemOrderList = redeemOrderList.stream().sorted(Comparator.comparing(x -> x.getTradeDt() + x.getTradeTime())).collect(Collectors.toList());
        String currentDt = DateUtils.formatToString(new Date(), DateUtils.YYYYMMDD);
        List<RedeemFeeNoticePo> newRedeemFeeNoticeInfoList = buildRedeemFeeNoticeList(currentDt, redeemOrderList);
        // 3.先删除之前的告警信息
        List<String> dealNoList = redeemOrderList.stream().map(RedeemNoticeOrderInfoVo::getDealNo).distinct().collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(dealNoList)){
            redeemFeeNoticeRepository.deleteByDealNo(dealNoList);
        }
        if (CollectionUtils.isEmpty(newRedeemFeeNoticeInfoList)) {
            log.info("没有需要添加的赎回预警信息,currentDt={},fundCode={},txAcctNo={}", currentDt, highDealOrderDtlPo.getFundCode(), highDealOrderDtlPo.getTxAcctNo());
            return;
        }
        // 4.新增本次的告警信息
        redeemFeeNoticeRepository.batchInsert(newRedeemFeeNoticeInfoList);
        // 5.预警是否需要存在,以及告警判断
        errorNotice(currentDt, newRedeemFeeNoticeInfoList);
    }

    /**
     * 构建赎回预警实体信息
     *
     * @param currentDt       当前日期
     * @param redeemOrderList 需要预警的订单信息
     * @return 赎回预警实体信息
     */
    private List<RedeemFeeNoticePo> buildRedeemFeeNoticeList(String currentDt, List<RedeemNoticeOrderInfoVo> redeemOrderList) {
        List<RedeemFeeNoticePo> newRedeemFeeNoticeInfoList = new ArrayList<>();
        for (RedeemNoticeOrderInfoVo redeemNoticeOrderInfoVo : redeemOrderList) {
            // 1.查询产品基础信息
            HighProductBaseInfoModel highProductBaseInfo = highProductService.getHighProductBaseInfo(redeemNoticeOrderInfoVo.getFundCode());
            if (highProductBaseInfo == null) {
                log.error("根据产品编码查询不到产品基础信息,fundCode={}", redeemNoticeOrderInfoVo.getFundCode());
                continue;
            }
            RedeemFeeNoticePo redeemFeeNoticePo = new RedeemFeeNoticePo();
            redeemFeeNoticePo.setDataDt(currentDt);
            redeemFeeNoticePo.setDealNo(redeemNoticeOrderInfoVo.getDealNo());
            redeemFeeNoticePo.setTxAcctNo(redeemNoticeOrderInfoVo.getTxAcctNo());
            redeemFeeNoticePo.setCustName(redeemNoticeOrderInfoVo.getCustName());
            redeemFeeNoticePo.setmBusiCode(BusinessCodeEnum.REDEEM.getMCode());
            redeemFeeNoticePo.setIsDelete(YesOrNoEnum.NO.getCode());
            redeemFeeNoticePo.setFundCode(redeemNoticeOrderInfoVo.getFundCode());
            redeemFeeNoticePo.setCurrentPeriodOpenDt(redeemNoticeOrderInfoVo.getSubmitTaDt());
            redeemFeeNoticePo.setFundName(redeemNoticeOrderInfoVo.getFundName());
            redeemFeeNoticePo.setApplyVol(redeemNoticeOrderInfoVo.getAppVol());
            redeemFeeNoticePo.setTradeDt(redeemNoticeOrderInfoVo.getTradeDt());
            redeemFeeNoticePo.setTradeTm(redeemNoticeOrderInfoVo.getTradeTime());
            redeemFeeNoticePo.setIsCycleProduct(highProductBaseInfo.getIsCyclicLock());
            // 2.查询下一个赎回日期
            Date currentDate = DateUtils.formatToDate(redeemNoticeOrderInfoVo.getTradeDt() + redeemNoticeOrderInfoVo.getTradeTime(), DateUtils.YYYYMMDDHHMMSS);
            setNextRedeemDt(highProductBaseInfo, currentDate, redeemFeeNoticePo);
            if (StringUtils.isBlank(redeemFeeNoticePo.getCurrentPeriodOpenDt()) || StringUtils.isBlank(redeemFeeNoticePo.getNextPeriodOpenDt())) {
                log.error("当前赎回开放日,与下一个赎回开放日中,有空值,直接返回,不需要预警,redeemFeeNoticePo={}", JSON.toJSONString(redeemFeeNoticePo));
                continue;
            }
            // 3.当前时间的赎回费
            try {
                setRedeemFeeInfo(redeemNoticeOrderInfoVo, redeemFeeNoticePo, highProductBaseInfo);
                // 有费用信息的才需要告警
                if (StringUtils.isNotBlank(redeemFeeNoticePo.getCurrentPeriodDetail())) {
                    newRedeemFeeNoticeInfoList.add(redeemFeeNoticePo);
                }
            } catch (Exception e) {
                log.error("设置当前赎回费信息异常,e:", e);
            }

        }
        return newRedeemFeeNoticeInfoList;
    }


    /**
     * 设置当前赎回费信息
     *
     * @param redeemNoticeOrderInfoVo 赎回订单信息
     * @param redeemFeeNoticePo       赎回费信息
     */
    private void setRedeemFeeInfo(RedeemNoticeOrderInfoVo redeemNoticeOrderInfoVo, RedeemFeeNoticePo redeemFeeNoticePo, HighProductBaseInfoModel highProductBaseInfo) throws Exception {
        log.info("设置当前赎回费信息-setCurrentRedeemFeeInfo,redeemNoticeOrderInfoVo={},highProductBaseInfo={},redeemFeeNoticePo={}", JSON.toJSONString(redeemNoticeOrderInfoVo), JSON.toJSONString(highProductBaseInfo), JSON.toJSONString(redeemFeeNoticePo));
        // 没有锁定期的赎回信息
        if (YesOrNoEnum.NO.getCode().equals(highProductBaseInfo.getHasLockPeriod())) {
            setByNoLockFundRedeemFeeInfo(redeemNoticeOrderInfoVo, redeemFeeNoticePo);
        } else {
            // 由锁定期的赎回信息
            setByLockFundRedeemFeeInfo(redeemNoticeOrderInfoVo, redeemFeeNoticePo);
        }
        log.info("设置当前赎回费信息-结果-setCurrentRedeemFeeInfo,redeemFeeNoticePo={}", JSON.toJSONString(redeemFeeNoticePo));
    }

    /**
     * 没有锁定期赎回信息
     *
     * @param redeemNoticeOrderInfoVo
     * @param redeemFeeNoticePo
     */
    private void setByNoLockFundRedeemFeeInfo(RedeemNoticeOrderInfoVo redeemNoticeOrderInfoVo, RedeemFeeNoticePo redeemFeeNoticePo) throws Exception {
        log.info("setByNoLockFundRedeemFeeInfo-没有锁定期赎回信息-redeemNoticeOrderInfoVo={},redeemFeeNoticePo={}", JSON.toJSONString(redeemNoticeOrderInfoVo), JSON.toJSONString(redeemFeeNoticePo));
        // 1.将客户所有确认成功或者部分成功的申购、认购、红利再投、强增、非交易过户转入、转托管转入、份额迁移转入的订单
        List<HighDealOrderDtlPo> highDealOrderDtlPoList = highDealOrderDtlRepository.queryAllConfirmBuyInOrder(redeemNoticeOrderInfoVo.getTxAcctNo(), redeemNoticeOrderInfoVo.getFundCode());
        highDealOrderDtlPoList = highDealOrderDtlPoList.stream().sorted(Comparator.comparing(HighDealOrderDtlPo::getAckDt).reversed()).collect(Collectors.toList());
        // 2.将该客户的所有未确认的申请成功的赎回订单按照申请时间从小到大排序
        List<RedeemNoticeOrderInfoVo> redeemOrderList = dealOrderRepository.selectUnConfrimRedeemOrder(redeemNoticeOrderInfoVo.getTxAcctNo(), redeemNoticeOrderInfoVo.getFundCode());
        redeemOrderList = redeemOrderList.stream().sorted(Comparator.comparing(x -> x.getTradeDt() + x.getTradeTime())).collect(Collectors.toList());
        // 3.根据被分摊到的订单的确认日期和赎回订单上报日期，计算差值得到本期赎回的每笔明细的持有天数
        setNoLockFeeInfo(redeemNoticeOrderInfoVo, redeemFeeNoticePo, highDealOrderDtlPoList, redeemOrderList);
    }

    /**
     * 无锁定期设置费用信息
     */
    private void setNoLockFeeInfo(RedeemNoticeOrderInfoVo redeemNoticeOrderInfoVo, RedeemFeeNoticePo redeemFeeNoticePo, List<HighDealOrderDtlPo> highDealOrderDtlPoList, List<RedeemNoticeOrderInfoVo> redeemOrderList) throws Exception {
        redeemFeeNoticePo.setIsMultiCard(YesOrNoEnum.NO.getCode());
        redeemFeeNoticePo.setEstimateDataSource(EstimateDataSourceEnum.ORDER.getDesc());
        // 份额分摊
        List<RedeemFeeDetailDto> redeemFeeDetailDtoList = new ArrayList<>();
        for (RedeemNoticeOrderInfoVo redeemOrder : redeemOrderList) {
            boolean isCurrentOrder = redeemNoticeOrderInfoVo.getDealNo().equals(redeemOrder.getDealNo());
            BigDecimal remainingAppVol = redeemOrder.getAppVol();
            if (remainingAppVol.compareTo(BigDecimal.ZERO) <= 0 && isCurrentOrder) {
                log.info("setNoLockFeeInfo-当前订单份额分摊结束,就不需要分摊了,redeemFeeDetailDtoList={}", JSON.toJSONString(redeemFeeDetailDtoList));
                break;
            }
            // 遍历份额明细，按登记日期顺序分摊
            for (HighDealOrderDtlPo highDealOrderDtlPo : highDealOrderDtlPoList) {
                if (remainingAppVol.compareTo(BigDecimal.ZERO) <= 0) {
                    break;
                }
                if (highDealOrderDtlPo.getAckVol().compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                BigDecimal availVol = highDealOrderDtlPo.getAckVol();
                BigDecimal usedVol;
                // 如果当前份额明细的可用份额大于0
                if (remainingAppVol.compareTo(availVol) > 0) {
                    usedVol = availVol;
                    highDealOrderDtlPo.setAckVol(BigDecimal.ZERO);
                    remainingAppVol = remainingAppVol.subtract(availVol);
                } else {
                    usedVol = remainingAppVol;
                    highDealOrderDtlPo.setAckVol(availVol.subtract(remainingAppVol));
                    remainingAppVol = BigDecimal.ZERO;
                }
                if (isCurrentOrder) {
                    RedeemFeeDetailDto redeemFeeDetailDto = new RedeemFeeDetailDto();
                    redeemFeeDetailDto.setBalanceVol(usedVol);
                    redeemFeeDetailDto.setBalanceDtNum(DateUtils.daysBetween(DateUtils.formatToDate(highDealOrderDtlPo.getAckDt(), DateUtils.YYYYMMDD), DateUtils.formatToDate(redeemNoticeOrderInfoVo.getSubmitTaDt(), DateUtils.YYYYMMDD)));
                    redeemFeeDetailDto.setNextBalanceDtNum(DateUtils.daysBetween(DateUtils.formatToDate(highDealOrderDtlPo.getAckDt(), DateUtils.YYYYMMDD), DateUtils.formatToDate(redeemFeeNoticePo.getNextPeriodOpenDt(), DateUtils.YYYYMMDD)));
                    redeemFeeDetailDtoList.add(redeemFeeDetailDto);
                }
            }
        }
        buildRedeemRateDetail(redeemNoticeOrderInfoVo, redeemFeeNoticePo, redeemFeeDetailDtoList);
    }

    /**
     * 设置赎回费详情
     *
     * @param redeemNoticeOrderInfoVo 需要赎回通知的订单信息
     * @param redeemFeeNoticePo       需落库的告警信息
     * @param redeemFeeDetailDtoList  赎回费率详情
     */
    private void buildRedeemRateDetail(RedeemNoticeOrderInfoVo redeemNoticeOrderInfoVo, RedeemFeeNoticePo redeemFeeNoticePo, List<RedeemFeeDetailDto> redeemFeeDetailDtoList) {
        // 计算赎回费率
        StringBuilder currentFeeDetail = new StringBuilder();
        StringBuilder nextFeeDetail = new StringBuilder();
        // 是否需要告警
        // 前后费用信息不一致,就需要告警
        boolean isNeedNotice = false;
        for (RedeemFeeDetailDto redeemFeeDetailDto : redeemFeeDetailDtoList) {
            List<FundFeeRateModel> currentFundFeeInfoForDays = queryFundInfoService.getFundFeeInfoForDay(Collections.singletonList(redeemNoticeOrderInfoVo.getFundCode()), BusinessCodeEnum.REDEEM.getCode(), redeemNoticeOrderInfoVo.getInvstType(), redeemFeeDetailDto.getBalanceDtNum());
            if (CollectionUtils.isNotEmpty(currentFundFeeInfoForDays)) {
                FundFeeRateModel fundFeeRateModel = currentFundFeeInfoForDays.get(0);
                redeemFeeDetailDto.setFeeRate(fundFeeRateModel.getFeeRate());
                currentFeeDetail.append(redeemFeeDetailDto.getBalanceDtNum()).append(",").append(redeemFeeDetailDto.getBalanceVol()).append(",").append(redeemFeeDetailDto.getFeeRate()).append(";");
            }
            List<FundFeeRateModel> nextFundFeeInfoForDays = queryFundInfoService.getFundFeeInfoForDay(Collections.singletonList(redeemNoticeOrderInfoVo.getFundCode()), BusinessCodeEnum.REDEEM.getCode(), redeemNoticeOrderInfoVo.getInvstType(), redeemFeeDetailDto.getNextBalanceDtNum());
            if (CollectionUtils.isNotEmpty(nextFundFeeInfoForDays)) {
                FundFeeRateModel fundFeeRateModel = nextFundFeeInfoForDays.get(0);
                redeemFeeDetailDto.setNextFeeRate(fundFeeRateModel.getFeeRate());
                nextFeeDetail.append(redeemFeeDetailDto.getNextBalanceDtNum()).append(",").append(redeemFeeDetailDto.getBalanceVol()).append(",").append(redeemFeeDetailDto.getNextFeeRate()).append(";");
            }
            if (redeemFeeDetailDto.getFeeRate() == null && redeemFeeDetailDto.getNextFeeRate() != null) {
                isNeedNotice = true;
                continue;
            }
            if (redeemFeeDetailDto.getFeeRate() != null && redeemFeeDetailDto.getNextFeeRate() == null) {
                isNeedNotice = true;
                continue;
            }
            if (redeemFeeDetailDto.getFeeRate() != null && redeemFeeDetailDto.getNextFeeRate() != null) {
                if (redeemFeeDetailDto.getFeeRate().compareTo(redeemFeeDetailDto.getNextFeeRate()) != 0) {
                    isNeedNotice = true;
                }
            }
        }
        redeemFeeNoticePo.setCurrentPeriodDetail(currentFeeDetail.toString());
        redeemFeeNoticePo.setNextPeriodDetail(nextFeeDetail.toString());
        redeemFeeNoticePo.setNoticeStatus(isNeedNotice ? RedeemFeeNoticeStatusEnum.NOT_NOTICE.getCode() : RedeemFeeNoticeStatusEnum.NO_NEED_NOTICE.getCode());

    }

    /**
     * 有锁定期的赎回信息设置
     *
     * @param redeemNoticeOrderInfoVo
     * @param redeemFeeNoticePo
     */
    private void setByLockFundRedeemFeeInfo(RedeemNoticeOrderInfoVo redeemNoticeOrderInfoVo, RedeemFeeNoticePo redeemFeeNoticePo) throws Exception {
        log.info("有锁定期的赎回信息设置,redeemNoticeOrderInfoVo={}", JSON.toJSONString(redeemNoticeOrderInfoVo));
        // 1.查询份额明细
        List<SubCustBooksPo> subCustBooksPoList = subCustBooksRepository.queryByOpenDt(redeemFeeNoticePo.getTxAcctNo(), redeemFeeNoticePo.getFundCode(), redeemNoticeOrderInfoVo.getSubmitTaDt());
        if (CollectionUtils.isEmpty(subCustBooksPoList)) {
            log.error("有锁定期,根据赎回开放日查询不到份额明细,txAcctNo={},fundCode={},submitTaDt={}", redeemFeeNoticePo.getTxAcctNo(), redeemFeeNoticePo.getFundCode(), redeemNoticeOrderInfoVo.getSubmitTaDt());
            return;
        }
        redeemFeeNoticePo.setEstimateDataSource(EstimateDataSourceEnum.SUB_BOOKS.getDesc());
        // 2.将份额明细按照份额登记日期从小到大排序
        subCustBooksPoList = subCustBooksPoList.stream().sorted(Comparator.comparing(SubCustBooksPo::getRegDt)).collect(Collectors.toList());
        // 3.将该客户的所有未确认的申请成功的赎回订单按照申请时间从小到大排序
        List<RedeemNoticeOrderInfoVo> redeemOrderList = dealOrderRepository.selectUnConfrimRedeemOrder(redeemNoticeOrderInfoVo.getTxAcctNo(), redeemNoticeOrderInfoVo.getFundCode());
        redeemOrderList = redeemOrderList.stream().sorted(Comparator.comparing(x -> x.getTradeDt() + x.getTradeTime())).collect(Collectors.toList());
        // 4.逐笔将赎回申请份额分摊到份额明细上
        setFeeInfo(redeemNoticeOrderInfoVo, redeemFeeNoticePo, subCustBooksPoList, redeemOrderList);
    }

    /**
     * 设置费用信息
     */
    private void setFeeInfo(RedeemNoticeOrderInfoVo redeemNoticeOrderInfoVo, RedeemFeeNoticePo redeemFeeNoticePo, List<SubCustBooksPo> subCustBooksPoList, List<RedeemNoticeOrderInfoVo> redeemOrderList) throws Exception {
        // 判断是否是多基金交易账号
        List<String> cpAcctNoList = subCustBooksPoList.stream().map(SubCustBooksPo::getCpAcctNo).distinct().collect(Collectors.toList());
        boolean isMultiCard = cpAcctNoList.size() > 1;
        redeemFeeNoticePo.setIsMultiCard(isMultiCard ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
        String maxRegDt = subCustBooksPoList.stream().map(SubCustBooksPo::getRegDt).max(Comparator.comparing(x -> x)).get();
        Date maxRegDate = DateUtils.formatToDate(maxRegDt, DateUtils.YYYYMMDD);
        List<RedeemFeeDetailDto> redeemFeeDetailDtoList = new ArrayList<>();
        // 份额分摊
        for (RedeemNoticeOrderInfoVo redeemOrder : redeemOrderList) {
            boolean isCurrentOrder = redeemNoticeOrderInfoVo.getDealNo().equals(redeemOrder.getDealNo());
            BigDecimal remainingAppVol = redeemOrder.getAppVol();
            if (remainingAppVol.compareTo(BigDecimal.ZERO) <= 0 && isCurrentOrder) {
                log.info("当前订单份额分摊结束,就不需要分摊了,redeemFeeDetailDtoList={}", JSON.toJSONString(redeemFeeDetailDtoList));
                break;
            }
            if (isCurrentOrder && isMultiCard) {
                // 如果是多卡的,那么计算持有天数,用登记日期最大的日期
                RedeemFeeDetailDto redeemFeeDetailDto = new RedeemFeeDetailDto();
                redeemFeeDetailDto.setBalanceVol(remainingAppVol);
                redeemFeeDetailDto.setBalanceDtNum(DateUtils.daysBetween(maxRegDate, DateUtils.formatToDate(redeemNoticeOrderInfoVo.getSubmitTaDt(), DateUtils.YYYYMMDD)));
                redeemFeeDetailDto.setNextBalanceDtNum(DateUtils.daysBetween(maxRegDate, DateUtils.formatToDate(redeemFeeNoticePo.getNextPeriodOpenDt(), DateUtils.YYYYMMDD)));
                redeemFeeDetailDtoList.add(redeemFeeDetailDto);
            } else {
                // 遍历份额明细，按登记日期顺序分摊
                for (SubCustBooksPo subCustBooksPo : subCustBooksPoList) {
                    if (remainingAppVol.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                    if (subCustBooksPo.getAvailVol().compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    BigDecimal availVol = subCustBooksPo.getAvailVol();
                    BigDecimal usedVol;
                    // 如果当前份额明细的可用份额大于0
                    if (remainingAppVol.compareTo(availVol) > 0) {
                        usedVol = availVol;
                        subCustBooksPo.setAvailVol(BigDecimal.ZERO);
                        remainingAppVol = remainingAppVol.subtract(availVol);
                    } else {
                        usedVol = remainingAppVol;
                        subCustBooksPo.setAvailVol(availVol.subtract(remainingAppVol));
                        remainingAppVol = BigDecimal.ZERO;
                    }
                    if (isCurrentOrder) {
                        RedeemFeeDetailDto redeemFeeDetailDto = new RedeemFeeDetailDto();
                        redeemFeeDetailDto.setSubCustBooksId(subCustBooksPo.getId());
                        redeemFeeDetailDto.setBalanceVol(usedVol);
                        // 如果是多卡的,那么计算持有天数,用登记日期最大的日期
                        redeemFeeDetailDto.setBalanceDtNum(DateUtils.daysBetween(DateUtils.formatToDate(subCustBooksPo.getRegDt(), DateUtils.YYYYMMDD), DateUtils.formatToDate(redeemNoticeOrderInfoVo.getSubmitTaDt(), DateUtils.YYYYMMDD)));
                        redeemFeeDetailDto.setNextBalanceDtNum(DateUtils.daysBetween(DateUtils.formatToDate(subCustBooksPo.getRegDt(), DateUtils.YYYYMMDD), DateUtils.formatToDate(redeemFeeNoticePo.getNextPeriodOpenDt(), DateUtils.YYYYMMDD)));
                        redeemFeeDetailDtoList.add(redeemFeeDetailDto);
                    }
                }
            }

        }
        // 计算赎回费率
        buildRedeemRateDetail(redeemNoticeOrderInfoVo, redeemFeeNoticePo, redeemFeeDetailDtoList);
    }

    /**
     * 获取下一个赎回日期
     * 预约赎回的基金：下一个赎回日=下一个开放日历的开放开始日
     * 每日开放的基金：下一个赎回日=当前工作日+7个工作日
     *
     * @param highProductBaseInfo 产品基础信息
     * @return 下一个赎回日
     */
    private void setNextRedeemDt(HighProductBaseInfoModel highProductBaseInfo, Date currentDate, RedeemFeeNoticePo redeemFeeNoticePo) {
        String fundCode = highProductBaseInfo.getFundCode();
        String nextRedeemDt = null;
        // 支持提前下单
        if (IsScheduledTradeEnum.SupportRedeemAdvance.getCode().equals(highProductBaseInfo.getIsScheduledTrade()) || IsScheduledTradeEnum.SupportBuyAndRedeemAdvance.getCode().equals(highProductBaseInfo.getIsScheduledTrade())) {
            // 查询预约信息
            ProductAppointmentInfoBean productAppointmentInfoBean = queryHighProductOuterService.queryAppointmentInfoByAppointDate(highProductBaseInfo.getFundCode(), "1", highProductBaseInfo.getShareClass(), highProductBaseInfo.getDisCode(), currentDate);
            // 支持提前下单的产品, 必须维护开放日历
            if (productAppointmentInfoBean == null) {
                log.error("产品支持预约,但是当前时间查询不到开放日历,fundCode={},currentDate={}", fundCode, currentDate);
                return;
            }
            // 找出开放截止日大于当前日历的最近的一个开放日历
            String openEndDt = productAppointmentInfoBean.getOpenEndDt();
            Date newDate = DateUtils.addDay(DateUtils.formatToDate(openEndDt, DateUtils.YYYYMMDD), 1);
            List<ProductAppointmentInfoModel> newProductAppointInfoList = productAppointmentInfoService.queryEffectOrWillEffectAppointListByOpenDt(Collections.singletonList(fundCode), "1", DateUtils.formatToString(newDate, DateUtils.YYYYMMDD));
            if (CollectionUtils.isEmpty(newProductAppointInfoList)) {
                log.error("产品支持预约,当前日历后面的日历没有,fundCode={},currentDate={},openEndDt={}", fundCode, currentDate, openEndDt);
                return;
            }
            // 下一个赎回日=下一个开放日历的开放开始日
            ProductAppointmentInfoModel productAppointmentInfoModel = newProductAppointInfoList.get(0);
            nextRedeemDt = productAppointmentInfoModel.getOpenStartDt();

        } else {
            // 当前工作日+7个工作日
            log.info("nextRedeemDt-产品不支持预约,下一个赎回日=当前工作日+7个工作日,fundCode={}", fundCode);
            String tradeDt = queryTradeDayOuterService.getWorkDay(currentDate);
            nextRedeemDt = queryTradeDayOuterService.addTradeDays(DateUtils.formatToDate(tradeDt, DateUtils.YYYYMMDD), 7);
        }
        log.info("nextRedeemDt-获取下一个赎回日期,nextRedeemDt={}", nextRedeemDt);
        redeemFeeNoticePo.setNextPeriodOpenDt(nextRedeemDt);
    }


}
