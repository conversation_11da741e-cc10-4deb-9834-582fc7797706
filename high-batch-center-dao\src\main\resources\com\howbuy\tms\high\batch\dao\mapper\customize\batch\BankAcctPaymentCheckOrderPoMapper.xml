<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.howbuy.tms.high.batch.dao.mapper.customize.batch.BankAcctPaymentCheckOrderPoMapper">
    <resultMap id="BaseResultMap" type="com.howbuy.tms.high.batch.dao.po.batch.BankAcctPaymentCheckOrderPo"
               extends="com.howbuy.tms.high.batch.dao.mapper.batch.BankAcctPaymentCheckOrderPoAutoMapper.BaseResultMap">
    </resultMap>

	<!-- 清理银行账户对账订单 -->
	<update id="deleteBankAcctPaymentCheckOrder">
		delete a from bank_acct_payment_check_order a where a.SYS_CODE = #{sysCode,jdbcType=VARCHAR}
	</update>

	<!-- 根据支付方式统计-支付订单统计总笔数/总金额 -->
	<resultMap id="TotalOrderNumAndTotalAmtMap"
		type="com.howbuy.tms.high.batch.dao.vo.PaymentOrderCountVo">
		<result column="payType" jdbcType="VARCHAR" property="payType" />
		<result column="totalOrderNum" jdbcType="DECIMAL" property="totalOrderNum" />
		<result column="totalAmt" jdbcType="DECIMAL" property="totalAmt" />
	</resultMap>
	<select id="selectTotalOrderNumAndTotalAmt" resultMap="TotalOrderNumAndTotalAmtMap" parameterType="map">
		select
		ifnull(count(1), 0) as totalOrderNum, ifnull(sum(t1.tx_amt), 0) as totalAmt
		from bank_acct_payment_check_order t1
		where t1.tx_pmt_flag = '2' and t1.PAYMENT_TYPE = '00' 
		and t1.sys_code = #{sysCode,jdbcType=VARCHAR}
		and t1.busi_code = #{busiCode,jdbcType=VARCHAR}
	</select>
	
	<update id="updateSysCode" parameterType="map">
	    update bank_acct_payment_check_order
	    set SYS_CODE = #{sysCode,jdbcType=VARCHAR}
	    where SYS_CODE IN ('93','94')
	</update>
  
    <!-- 支付对账订单结果Map -->
	<resultMap id="PmtCheckAckResult" type="com.howbuy.tms.high.batch.dao.vo.PmtCheckAckVo">
	    <result column="PMT_DEAL_NO" jdbcType="VARCHAR" property="pmtDealNo" />
	    <result column="PMT_AMT" jdbcType="DECIMAL" property="pmtAmt" />
	    <result column="PAYMENT_TYPE" jdbcType="VARCHAR" property="paymentType" />
 	</resultMap>
  
    <select id="selectPaymentOrderForPayChkAck" parameterType="map" resultMap="PmtCheckAckResult">
		 select t.DEAL_ORDER_NO as PMT_DEAL_NO, t.tx_amt as PMT_AMT
		  from bank_acct_payment_check_order t
		 where t.tx_pmt_flag = '2' and t.PAYMENT_TYPE = '00' 
			and t.sys_code = #{sysCode,jdbcType=VARCHAR}
			and t.busi_code = #{busiCode,jdbcType=VARCHAR}
	</select>

    <!-- 统计当前TA未对账的订单数量 -->
    <select id="countBankPaymentUnCheckOrderNum" parameterType="map" resultType="java.lang.Integer">
	   	select
	   		count(1)
  		from
		    BANK_ACCT_PAYMENT_CHECK_ORDER s
  		left join
  			payment_order p
    	on s.deal_order_no = p.pmt_deal_no
  		left join
  			HIGH_DEAL_ORDER_DTL f
    	on p.deal_no = f.deal_no
    	where s.sys_code = '90'
    		and f.ta_code = #{taCode,jdbcType=VARCHAR}
    		and s.pmt_comp_flag <![CDATA[ <> ]]> '02'
   </select>

  <delete id="deleteBySysCode">
    delete from bank_acct_payment_check_order where sys_code = #{sysCode,jdbcType=VARCHAR}
    </delete>

	<insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
		<!--@mbg.generated-->
		insert into bank_acct_payment_check_order
		(payment_order_no, deal_order_no, busi_code, pay_channel, dis_code, tx_type, tx_acct_no,
		product_code, bank_acct, product_dt, pmt_dtm, payment_type, pmt_comp_dtm, tx_pmt_flag,
		currency, tx_amt, tx_total_amt, bank_code, fee, record_no, sys_code, pmt_comp_flag,
		pmt_tx_code, ori_pay_no, ori_trade_no, task_id)
		values
		<foreach collection="list" item="item" separator=",">
			(#{item.paymentOrderNo,jdbcType=VARCHAR}, #{item.dealOrderNo,jdbcType=VARCHAR}, #{item.busiCode,jdbcType=VARCHAR},
			#{item.payChannel,jdbcType=VARCHAR}, #{item.disCode,jdbcType=VARCHAR}, #{item.txType,jdbcType=VARCHAR},
			#{item.txAcctNo,jdbcType=VARCHAR}, #{item.productCode,jdbcType=VARCHAR}, #{item.bankAcct,jdbcType=VARCHAR},
			#{item.productDt,jdbcType=VARCHAR}, #{item.pmtDtm,jdbcType=VARCHAR}, #{item.paymentType,jdbcType=VARCHAR},
			#{item.pmtCompDtm,jdbcType=VARCHAR}, #{item.txPmtFlag,jdbcType=VARCHAR}, #{item.currency,jdbcType=VARCHAR},
			#{item.txAmt,jdbcType=DECIMAL}, #{item.txTotalAmt,jdbcType=DECIMAL}, #{item.bankCode,jdbcType=VARCHAR},
			#{item.fee,jdbcType=DECIMAL}, #{item.recordNo,jdbcType=VARCHAR}, #{item.sysCode,jdbcType=VARCHAR},
			#{item.pmtCompFlag,jdbcType=VARCHAR}, #{item.pmtTxCode,jdbcType=VARCHAR}, #{item.oriPayNo,jdbcType=VARCHAR},
			#{item.oriTradeNo,jdbcType=VARCHAR}, #{item.taskId,jdbcType=VARCHAR})
		</foreach>
	</insert>

</mapper>