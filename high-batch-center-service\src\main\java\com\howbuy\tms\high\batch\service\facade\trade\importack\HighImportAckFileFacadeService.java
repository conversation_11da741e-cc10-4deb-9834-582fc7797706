/**
 *Copyright (c) 2017, <PERSON><PERSON><PERSON><PERSON> HOWBUY INVESTMENT MANAGEMENT Co., Ltd.
 *All right reserved.
 *
 *THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF HOWBUY INVESTMENT
 *MANAGEMENT CO., LTD.  THE CONTENTS OF THIS FILE MAY NOT BE DISCLOSED
 *TO THIRD PARTIES, COPIED OR DUPLICATED IN ANY FORM, IN WHOLE OR IN PART,
 *WITHOUT THE PRIOR WRITTEN PERMISSION OF HOWBUY INVESTMENT MANAGEMENT
 * CO., LTD.
 */

package com.howbuy.tms.high.batch.service.facade.trade.importack;

import com.alibaba.fastjson.JSON;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.howbuy.tms.cache.service.lock.LockService;
import com.howbuy.tms.common.constant.ExceptionCodes;
import com.howbuy.tms.common.enums.database.SysCodeEnum;
import com.howbuy.tms.common.exception.BusinessException;
import com.howbuy.tms.high.batch.facade.trade.importack.HighImportAckFileFacade;
import com.howbuy.tms.high.batch.facade.trade.importack.HighImportAckFileRequest;
import com.howbuy.tms.high.batch.facade.trade.importack.HighImportAckFileResponse;
import com.howbuy.tms.high.batch.service.common.MessageSource;
import com.howbuy.tms.high.batch.service.common.ThreadExceptionStatus;
import com.howbuy.tms.high.batch.service.exception.BatchException;
import com.howbuy.tms.high.batch.service.facade.trade.task.ImportAckFileTask;
import com.howbuy.tms.high.batch.service.service.batch.importack.ImportAckService;
import com.howbuy.tms.high.batch.service.service.batch.workday.WorkdayService;
import com.howbuy.trace.thread.ThreadTraceHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @description:高端导入确认
 * <AUTHOR>
 * @date 2017年7月5日 下午5:54:11
 * @since JDK 1.6
 */
@DubboService
@Service("highImportAckFileFacadeService")
public class HighImportAckFileFacadeService implements HighImportAckFileFacade {

    private static Logger logger = LogManager.getLogger(HighImportAckFileFacadeService.class);

    @Autowired
    private ImportAckService importAckService;
    
    @Autowired
    private WorkdayService workdayService;
    @Qualifier("cache.lockService")
    @Autowired
    private LockService lockService;
    /**
     * 队列容量
     */
    private static final int POOL_CAPACITY = 10240;

    /**HighImportAckFileFacadeService
     * 线程池
     */
    private ExecutorService threadPool = createFixedThreadPool("HighImportAckFileFacadeService",8);


    @Override
    public HighImportAckFileResponse execute(HighImportAckFileRequest request) {

        logger.info("开始导入确认文件:{}", JSON.toJSONString(request));
        HighImportAckFileResponse resp = new HighImportAckFileResponse();
        resp.setReturnCode(ExceptionCodes.SUCCESS);
        resp.setDescription(MessageSource.getMessageByCode(ExceptionCodes.SUCCESS));
        String tradeDt = workdayService.getSaleSysCurrWorkay();
        List<String> taCodeList = request.getTaCodeList();
        String sysCode = SysCodeEnum.BATCH_HIGH.getCode();
        if (CollectionUtils.isEmpty(taCodeList)) {
            throw new BatchException(ExceptionCodes.PARAM_IS_NULL, "未选择TA");
        }
        // 处理
        process(taCodeList, tradeDt, sysCode, resp);
        // 检查所有TA业务是否做完，更新主业务表
        checkAllEnd(tradeDt, sysCode);
        
        logger.info("导入确认文件执行结束:{}", JSON.toJSONString(resp));
        return resp;
    }
    /**
     * 
     * process:多线程处理
     * @param taCodeList
     * @param tradeDt
     * @param sysCode
     * @param resp
     * <AUTHOR>
     * @date 2018年12月5日 下午4:06:50
     */
    private void process(List<String> taCodeList, String tradeDt, String sysCode, HighImportAckFileResponse resp) {
        List<ThreadExceptionStatus> exList = new ArrayList<>();
        ThreadExceptionStatus exStatus = null;
        CountDownLatch latch = new CountDownLatch(taCodeList.size());
        for (String taCode : taCodeList) {
            exStatus = new ThreadExceptionStatus();
            exList.add(exStatus);
            exStatus.setTaCode(taCode);
            threadPool.execute(ThreadTraceHelper.decorate(new ImportAckFileTask(taCode, tradeDt, sysCode, importAckService, exStatus, latch, lockService)));
        }
        try {
            latch.await();
        } catch (InterruptedException e) {
            logger.error("latch await error.", e);
            Thread.currentThread().interrupt();
        }
        // 异常处理
        processEx(exList, resp);
    }
    
    private void processEx(List<ThreadExceptionStatus> resultList, HighImportAckFileResponse resp) {
        BusinessException bizEx = null;
        StringBuffer errorCode = new StringBuffer();
        StringBuffer errorMsg = new StringBuffer();
        List<String> needWarnMsg = new LinkedList<>();
        for (ThreadExceptionStatus exStatus : resultList) {
            if (exStatus.isExsitException()) {
                if (exStatus.getException() instanceof BusinessException) {
                    bizEx = (BusinessException) exStatus.getException();
                    if (StringUtils.isEmpty(errorCode)) {
                        errorCode.append("TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(bizEx.getErrorCode());
                    } else {
                        errorCode.append(",TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(bizEx.getErrorCode());
                    }
                    errorMsg.append("TA:" + exStatus.getTaCode() + "," + bizEx.getErrorDesc() + "\n");
                    if (bizEx.isNeedWarn()) {
                        needWarnMsg.add("TA:" + exStatus.getTaCode() + "," + bizEx.getErrorDesc());
                    }
                } else {
                    if (StringUtils.isEmpty(errorCode)) {
                        errorCode.append("TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(ExceptionCodes.HIGH_BATCH_CENTER_IMPORT_ACK_FILE_PROCESS_FAIL);
                    } else {
                        errorCode.append(",TA:" + exStatus.getTaCode() + ",");
                        errorCode.append(ExceptionCodes.HIGH_BATCH_CENTER_IMPORT_ACK_FILE_PROCESS_FAIL);
                    }
                    errorMsg.append("TA:" + exStatus.getTaCode() + ",批处理执行失败！导入确认发生异常\n");
                    needWarnMsg.add("TA:" + exStatus.getTaCode() + ",批处理执行失败！导入确认发生异常");
                }
            }
        }
        if (!StringUtils.isEmpty(errorCode)) {
            resp.setReturnCode(errorCode.toString());
            resp.setDescription(errorMsg.toString());
            resp.setNeedWarnMsgs(needWarnMsg);
        }
    }
    
    /**
     * 
     * checkAllEnd:当所有TA处理完，更新主业务表
     * @param tradeDt
     * @param sysCode
     * <AUTHOR>
     * @date 2018年12月5日 下午4:00:18
     */
    private void checkAllEnd(String tradeDt, String sysCode) {
        importAckService.endAllOff(tradeDt, sysCode);
    }


    public static ExecutorService createFixedThreadPool(String threadName, int poolSize) {
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat(threadName).build();
        return new ThreadPoolExecutor(poolSize, poolSize,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(POOL_CAPACITY), namedThreadFactory, new ThreadPoolExecutor.AbortPolicy());
    }
}
